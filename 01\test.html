<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>哪吒之魔童闹海 - 推广曲歌词</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#FF4D4F',
                        secondary: '#1890FF',
                        accent: '#FFB800',
                        dark: '#1F2937',
                        light: '#F9FAFB'
                    },
                    fontFamily: {
                        display: ['"Ma Shan Zheng"', 'cursive'],
                        sans: ['"PingFang SC"', 'Inter', 'system-ui', 'sans-serif']
                    }
                    
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .page-a4 {
                width: 210mm;
                min-height: 297mm;
                margin: 10mm auto;
                background: white;
                box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
                position: relative;
                overflow: hidden;
            }
            .lyric-column {
                column-count: 2;
                column-gap: 20px;
            }
            .character-badge {
                @apply h-10 w-10 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3 shadow-md;
            }
            .nezha-badge {
                @apply character-badge bg-gradient-to-br from-red-500 to-red-700;
            }
            .aobing-badge {
                @apply character-badge bg-gradient-to-br from-blue-400 to-blue-700;
            }
            .hechang-badge {
                @apply character-badge bg-gradient-to-br from-purple-500 to-purple-700;
            }
            .decorative-border {
                position: absolute;
                height: 100%;
                width: 2px;
                background: linear-gradient(to bottom, transparent, #FF4D4F, transparent);
                left: 50%;
                transform: translateX(-50%);
                z-index: 0;
            }
            .illustration-container {
                position: absolute;
                top: 30mm;
                right: 20mm;
                width: 60mm;
                height: 80mm;
                z-index: 1;
                border-radius: 50%;
                overflow: hidden;
                box-shadow: 0 5mm 10mm rgba(0, 0, 0, 0.2);
            }
            .illustration-container img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            .corner-decoration {
                position: absolute;
                width: 40mm;
                height: 40mm;
                z-index: 0;
            }
            
            @media print {
                body * {
                    visibility: hidden;
                }
                .page-a4, .page-a4 * {
                    visibility: visible;
                }
                .page-a4 {
                    position: absolute;
                    left: 0;
                    top: 0;
                    margin: 0;
                    width: 100%;
                    height: 100%;
                }
                .no-print {
                    display: none !important;
                }
                .illustration-container {
                    box-shadow: none;
                }
            }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Ma+Shan+Zheng&family=PingFang+SC:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body class="bg-gray-50 min-h-screen font-sans text-dark">
    <div class="container mx-auto px-4 py-8">
        <!-- 顶部导航栏 -->
        <header class="no-print mb-8">
            <div class="flex justify-between items-center bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center space-x-3">
                    <div class="h-10 w-10 rounded-full bg-gradient-red flex items-center justify-center">
                        <i class="fa fa-music text-white"></i>
                    </div>
                    <h1 class="text-xl font-bold text-dark">哪吒歌词打印</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="print-btn" class="bg-gradient-red hover:opacity-90 text-white px-5 py-2 rounded-lg shadow-md transition-all duration-300 flex items-center">
                        <i class="fa fa-print mr-2"></i>
                        <span>打印歌词</span>
                    </button>
                    <button id="download-btn" class="bg-gradient-blue hover:opacity-90 text-white px-5 py-2 rounded-lg shadow-md transition-all duration-300 flex items-center">
                        <i class="fa fa-download mr-2"></i>
                        <span>保存PDF</span>
                    </button>
                    <button id="export-image-btn" class="bg-green-500 hover:bg-green-600 text-white px-5 py-2 rounded-lg shadow-md transition-all duration-300 flex items-center">
                        <i class="fa fa-image mr-2"></i>
                        <span>导出图片</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- A4页面容器 -->
        <main class="mb-8">
            <div class="page-a4 relative">
                <!-- 装饰元素 -->
                <div class="decorative-border"></div>
                <div class="corner-decoration top-left"></div>
                <div class="corner-decoration bottom-right"></div>
                
                

                <!-- 页面标题 -->
                <div class="text-center pt-10 pb-6 relative z-10">
                    <h1 class="text-[clamp(2.5rem,5vw,4rem)] font-display font-bold text-primary mb-4 text-shadow">我乃哪吒三太子</h1>
                    <p class="text-2xl text-gray-600 italic max-w-2xl mx-auto font-medium">《哪吒之魔童闹海》电影推广曲 </p>
                </div>

                <!-- 歌词内容 - 左右分栏 -->
                <div class="lyric-column p-8 relative z-10">
                    <!-- 第一段 - 哪吒 -->
                    <div class="mb-6">
                        <div class="flex items-center mb-2">
                            <span class="nezha-badge">哪</span>
                            <h2 class="text-2xl font-bold text-primary">哪吒：</h2>
                        </div>
                        <ul class="list-disc pl-5 space-y-3 text-gray-800 text-2xl font-medium">
                            <li>小爷我生来就无畏</li>
                            <li>管它是老子还是谁</li>
                            <li>若师父拿出戒尺规</li>
                            <li>也休想把我来指挥</li>
                        </ul>
                    </div>

                    <!-- 第二段 - 哪吒 -->
                    <div class="mb-6">
                        <div class="flex items-center mb-2">
                            <span class="nezha-badge">哪</span>
                            <h2 class="text-2xl font-bold text-primary">哪吒：</h2>
                        </div>
                        <ul class="list-disc pl-5 space-y-3 text-gray-800 text-2xl font-medium">
                            <li>敢问上前是哪一位</li>
                            <li>大道理神仙 你不配</li>
                            <li>我管你是什么段位</li>
                            <li>反正赶上我只能认倒霉</li>
                        </ul>
                    </div>

                    <!-- 第三段 - 敖丙 -->
                    <div class="mb-6">
                        <div class="flex items-center mb-2">
                            <span class="aobing-badge">丙</span>
                            <h2 class="text-2xl font-bold text-secondary">敖丙：</h2>
                        </div>
                        <ul class="list-disc pl-5 space-y-3 text-gray-800 text-2xl font-medium">
                            <li>做人做仙做魔鬼</li>
                            <li>总有人要反对</li>
                            <li>做天地人间独一类</li>
                            <li>大胆走一回</li>
                        </ul>
                    </div>

                    <!-- 第四段 - 哪吒 -->
                    <div class="mb-6">
                        <div class="flex items-center mb-2">
                            <span class="nezha-badge">哪</span>
                            <h2 class="text-2xl font-bold text-primary">哪吒：</h2>
                        </div>
                        <ul class="list-disc pl-5 space-y-3 text-gray-800 text-2xl font-medium">
                            <li>我乃哪吒三太子</li>
                            <li>放纵不羁爱作诗</li>
                            <li>双手插兜大步走</li>
                            <li>曲道也能踩成直</li>
                        </ul>
                    </div>

                    <!-- 第五段 - 哪吒 -->
                    <div class="mb-6">
                        <div class="flex items-center mb-2">
                            <span class="nezha-badge">哪</span>
                            <h2 class="text-2xl font-bold text-primary">哪吒：</h2>
                        </div>
                        <ul class="list-disc pl-5 space-y-3 text-gray-800 text-2xl font-medium">
                            <li>走过陈塘关外关</li>
                            <li>人间月儿圆又圆</li>
                            <li>我已斗过天上天</li>
                            <li>不入眼的仙中仙</li>
                        </ul>
                    </div>

                    <!-- 第六段 - 合唱 -->
                    <div class="mb-6">
                        <div class="flex items-center mb-2">
                            <span class="hechang-badge">合</span>
                            <h2 class="text-2xl font-bold text-purple-600">合：</h2>
                        </div>
                        <ul class="list-disc pl-5 space-y-3 text-gray-800 text-2xl font-medium">
                            <li>就算要把我的血肉撕碎千遍</li>
                            <li>我还是一如从前</li>
                        </ul>
                    </div>
                    <div class="mb-6"></div>
                        <br><br><br>
                    <!-- 第七段 - 哪吒/敖丙 -->
                    <div class="mb-6">
                        <div class="flex flex-wrap gap-3 mb-2">
                            <div class="flex items-center">
                                <span class="nezha-badge">哪</span>
                                <h2 class="text-2xl font-bold text-primary">哪吒：</h2>
                            </div>
                            <div class="flex items-center">
                                <span class="aobing-badge">丙</span>
                                <h2 class="text-2xl font-bold text-secondary">敖丙：</h2>
                            </div>
                        </div>
                        <ul class="list-disc pl-5 space-y-3 text-gray-800 text-2xl font-medium">
                            <li>做万丈火焰</li>
                            <li>做逆鳞一片</li>
                        </ul>
                    </div>
                    
                    <!-- 第八段 - 合唱 -->
                    <div class="mb-6">
                        <div class="flex items-center mb-2">
                            <span class="hechang-badge">合</span>
                            <h2 class="text-2xl font-bold text-purple-600">合：</h2>
                        </div>
                        <ul class="list-disc pl-5 space-y-3 text-gray-800 text-2xl font-medium">
                            <li>做名为自己的瞬间</li>
                            <li>我要在这个天地间走上一回</li>
                            <li>不曾改年少容颜</li>
                            <li>踏破千层浪 把山摇地晃</li>
                            <li>那又怎样</li>
                        </ul>
                    </div>

                    <!-- 第九段 - 敖丙 -->
                    <div class="mb-6">
                        <div class="flex items-center mb-2">
                            <span class="aobing-badge">丙</span>
                            <h2 class="text-2xl font-bold text-secondary">敖丙：</h2>
                        </div>
                        <ul class="list-disc pl-5 space-y-3 text-gray-800 text-2xl font-medium">
                            <li>上天入海谋仙位</li>
                            <li>怎么做都不对</li>
                            <li>别管我荒唐这一回</li>
                            <li>问我心无愧</li>
                        </ul>
                    </div>

                    <!-- 第十段 - 申公豹/太乙 -->
                    <div class="mb-6">
                        <div class="flex flex-wrap gap-3 mb-2">
                            <div class="flex items-center">
                                <span class="character-badge bg-gray-600">豹</span>
                                <h2 class="text-2xl font-bold text-gray-600">申公豹：</h2>
                            </div>
                            <div class="flex items-center">
                                <span class="character-badge bg-yellow-500">乙</span>
                                <h2 class="text-2xl font-bold text-yellow-600">太乙：</h2>
                            </div>
                        </div>
                        <ul class="list-disc pl-5 space-y-3 text-gray-800 text-2xl font-medium">
                            <li>啊～～我也要要要唱</li>
                            <li>哈哈哈哈 你来唱啥子嘛</li>
                            <li>谁 说我不不不行</li>
                            <li>哎呀 你莫捣乱了</li>
                        </ul>
                    </div>

                    <!-- 第十一段 - 敖丙 -->
                    <div class="mb-6">
                        <div class="flex items-center mb-2">
                            <span class="aobing-badge">丙</span>
                            <h2 class="text-2xl font-bold text-secondary">敖丙：</h2>
                        </div>
                        <ul class="list-disc pl-5 space-y-3 text-gray-800 text-2xl font-medium">
                            <li>我已看过山外山</li>
                            <li>家乡月儿圆又圆</li>
                            <li>我已斗过天上天</li>
                            <li>不入眼的仙中仙</li>
                        </ul>
                    </div>

                    <!-- 第十二段 - 合唱 -->
                    <div class="mb-6">
                        <div class="flex items-center mb-2">
                            <span class="hechang-badge">合</span>
                            <h2 class="text-2xl font-bold text-purple-600">合：</h2>
                        </div>
                        <ul class="list-disc pl-5 space-y-3 text-gray-800 text-2xl font-medium">
                            <li>就算要把我的血肉撕碎千遍</li>
                            <li>我还是一如从前</li>
                        </ul>
                    </div>

                    <!-- 第十三段 - 哪吒/敖丙 -->
                    <div class="mb-6">
                        <div class="flex flex-wrap gap-3 mb-2">
                            <div class="flex items-center">
                                <span class="nezha-badge">哪</span>
                                <h2 class="text-2xl font-bold text-primary">哪吒：</h2>
                            </div>
                            <div class="flex items-center">
                                <span class="aobing-badge">丙</span>
                                <h2 class="text-2xl font-bold text-secondary">敖丙：</h2>
                            </div>
                        </div>
                        <ul class="list-disc pl-5 space-y-3 text-gray-800 text-2xl font-medium">
                            <li>做万丈火焰</li>
                            <li>做逆鳞一片</li>
                        </ul>
                    </div>

                    <!-- 第十四段 - 合唱 -->
                    <div class="mb-6">
                        <div class="flex items-center mb-2">
                            <span class="hechang-badge">合</span>
                            <h2 class="text-2xl font-bold text-purple-600">合：</h2>
                        </div>
                        <ul class="list-disc pl-5 space-y-3 text-gray-800 text-2xl font-medium">
                            <li>做名为自己的瞬间</li>
                            <li>我要去那天地之外走上一回</li>
                            <li>这就是我不会变</li>
                            <li>踏破千层浪 把山摇地晃</li>
                            <li>那又怎样</li>
                        </ul>
                    </div>
                </div>

                <!-- 页脚 -->
                <footer class="mt-10 pt-4 border-t border-gray-200 text-center text-gray-500 text-sm relative z-10">
                    <p></p>
                </footer>
            </div>
        </main>

        <!-- 页脚信息 -->
        <footer class="no-print text-center text-gray-500 text-sm py-4">
            <p>点击"打印歌词"按钮可导出为A4尺寸PDF，或直接使用浏览器打印功能</p>
            <p class="mt-1">最佳打印设置：无边距，缩放100%</p>
        </footer>
    </div>

    <script>
        // 设置当前日期（已删除相关功能）
        document.addEventListener('DOMContentLoaded', function() {
            // 打印按钮事件
            document.getElementById('print-btn').addEventListener('click', function() {
                window.print();
            });

            // 下载PDF按钮事件（使用浏览器打印为PDF功能）
            document.getElementById('download-btn').addEventListener('click', function() {
                // 模拟打印对话框，用户可选择保存为PDF
                window.print();
            });

            // 导出图片按钮事件
            document.getElementById('export-image-btn').addEventListener('click', function() {
                const pageElement = document.querySelector('.page-a4');
                const button = this;
                const originalText = button.innerHTML;

                // 显示加载状态
                button.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i><span>生成中...</span>';
                button.disabled = true;

                // 使用html2canvas生成图片
                html2canvas(pageElement, {
                    scale: 2, // 提高图片质量
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    width: pageElement.offsetWidth,
                    height: pageElement.offsetHeight
                }).then(function(canvas) {
                    // 创建下载链接
                    const link = document.createElement('a');
                    link.download = '哪吒歌词_' + new Date().toISOString().slice(0, 10) + '.png';
                    link.href = canvas.toDataURL('image/png');

                    // 触发下载
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // 恢复按钮状态
                    button.innerHTML = originalText;
                    button.disabled = false;
                }).catch(function(error) {
                    console.error('导出图片失败:', error);
                    alert('导出图片失败，请重试');

                    // 恢复按钮状态
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
            });
        });
    </script>
</body>
</html>
    