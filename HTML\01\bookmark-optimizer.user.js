// ==UserScript==
// @name 书签页面优化 - SortableJS拖拽 - Shadcn/UI风格
// @namespace http://tampermonkey.net/
// @version 1.7
// @description 使用SortableJS实现书签页面拖拽排序，采用Shadcn/UI设计风格
// <AUTHOR>
// @match http://t.fn:50005/*
// @grant GM_getValue
// @grant GM_setValue
// @grant GM_registerMenuCommand
// @require https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js
// ==/UserScript==

(function () {
    'use strict';

    // 添加iOS风格样式
    const style = document.createElement('style');
    style.innerHTML = `
    /* 全局背景和容器样式 - Shadcn/UI风格 */
    :root {
        --background: 0 0% 100%;
        --foreground: 222.2 84% 4.9%;
        --card: 0 0% 100%;
        --card-foreground: 222.2 84% 4.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 222.2 84% 4.9%;
        --primary: 221.2 83.2% 53.3%;
        --primary-foreground: 210 40% 98%;
        --secondary: 210 40% 96.1%;
        --secondary-foreground: 222.2 47.4% 11.2%;
        --muted: 210 40% 96.1%;
        --muted-foreground: 215.4 16.3% 46.9%;
        --accent: 210 40% 96.1%;
        --accent-foreground: 222.2 47.4% 11.2%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 40% 98%;
        --border: 214.3 31.8% 91.4%;
        --input: 214.3 31.8% 91.4%;
        --ring: 221.2 83.2% 53.3%;
        --radius: 0.5rem;
    }

    body {
        background-color: hsl(var(--background));
        color: hsl(var(--foreground));
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        min-height: 100vh;
        position: relative;
    }
    
    #container-bookmakrs {
        display: flex !important;
        gap: 16px;
        margin: 16px;
        max-width: 1200px;
        margin-left: auto;
        margin-right: auto;
    }

    /* 左侧导航 - Shadcn/UI风格 */
    .bookmark-nav {
        width: 250px;
        background: hsl(var(--card));
        border-radius: var(--radius);
        padding: 16px;
        height: fit-content;
        border: 1px solid hsl(var(--border));
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        will-change: transform;
    }

    .nav-title {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 16px;
        color: hsl(var(--muted-foreground));
        letter-spacing: 0.05em;
    }
    
    .nav-title::before {
        content: "📑";
        margin-right: 8px;
        font-size: 16px;
    }

    .nav-groups {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .nav-group-item {
        display: flex;
        align-items: center;
        padding: 10px 12px;
        margin-bottom: 2px;
        border-radius: var(--radius);
        cursor: move;
        font-size: 14px;
        color: hsl(var(--foreground));
        transition: background-color 0.2s ease, color 0.2s ease;
        user-select: none;
        position: relative;
        background: transparent;
        border: none;
    }

    .nav-group-item:hover {
        background: hsl(var(--accent));
        color: hsl(var(--accent-foreground));
    }

    .nav-group-item.active {
        background: hsl(var(--accent));
        color: hsl(var(--accent-foreground));
        font-weight: 500;
    }

    .nav-group-item.sortable-chosen {
        opacity: 0.7;
        transform: scale(1.01);
        z-index: 1000;
        background: hsl(var(--accent));
        color: hsl(var(--accent-foreground));
    }

    .nav-group-item.sortable-ghost {
        opacity: 0.5;
        background: hsl(var(--accent));
        border: 1px dashed hsl(var(--border));
    }

    .nav-group-item.sortable-drag {
        transition: transform 0.2s ease;
        background: hsl(var(--accent));
        color: hsl(var(--accent-foreground));
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    @keyframes pulse {
        0% { box-shadow: 0 5px 15px rgba(0,122,255,0.2); }
        50% { box-shadow: 0 5px 25px rgba(0,122,255,0.4); }
        100% { box-shadow: 0 5px 15px rgba(0,122,255,0.2); }
    }

    .nav-group-index {
        width: 20px;
        height: 20px;
        background: hsl(var(--muted));
        color: hsl(var(--muted-foreground));
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        font-weight: 500;
        margin-right: 10px;
    }
    
    .nav-group-icon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        margin-right: 10px;
        flex-shrink: 0;
    }

    .nav-group-item.active .nav-group-index {
        background: hsl(var(--primary));
        color: hsl(var(--primary-foreground));
    }

    .nav-group-name {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* 右侧内容 - Shadcn/UI风格 */
    .bookmark-content {
        flex: 1;
        background: hsl(var(--card));
        border-radius: var(--radius);
        border: 1px solid hsl(var(--border));
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .content-header {
        padding: 16px;
        border-bottom: 1px solid hsl(var(--border));
        background: hsl(var(--card));
        display: flex;
        align-items: center;
    }

    .content-header h3 {
        margin: 0;
        color: hsl(var(--foreground));
        font-size: 16px;
        font-weight: 600;
    }
    
    .content-header::before {
        content: "📂";
        margin-right: 8px;
        font-size: 16px;
    }

    /* 搜索框样式 */
    .search-container {
        padding: 16px 16px 8px;
        border-bottom: 1px solid hsl(var(--border));
    }
    
    .search-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        width: 100%;
    }
    
    .search-icon {
        position: absolute;
        left: 10px;
        color: hsl(var(--muted-foreground));
        font-size: 14px;
        pointer-events: none;
    }
    
    .search-input {
        width: 100%;
        padding: 8px 8px 8px 32px;
        border-radius: var(--radius);
        border: 1px solid hsl(var(--border));
        background: hsl(var(--background));
        font-size: 14px;
        color: hsl(var(--foreground));
        outline: none;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }
    
    .search-input:focus {
        border-color: hsl(var(--ring));
        box-shadow: 0 0 0 2px hsla(var(--ring), 0.2);
    }
    
    .search-input::placeholder {
        color: hsl(var(--muted-foreground));
    }

    .bookmarks-list {
        padding: 16px;
        min-height: 300px;
    }

    /* 书签项 - Shadcn/UI风格 */
    .bookmark-item {
        display: flex;
        align-items: center;
        padding: 12px;
        margin: 0 0 8px 0;
        background: hsl(var(--card));
        border-radius: var(--radius);
        border: 1px solid hsl(var(--border));
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        transition: background-color 0.2s ease, 
                    border-color 0.2s ease,
                    box-shadow 0.2s ease;
        cursor: move;
        position: relative;
        user-select: none;
    }

    .bookmark-item:hover {
        background: hsl(var(--accent));
        border-color: hsl(var(--border));
    }

    .bookmark-item.sortable-chosen {
        opacity: 0.7;
        transform: scale(1.01);
        z-index: 1000;
        background: hsl(var(--accent));
        color: hsl(var(--accent-foreground));
    }

    .bookmark-item.sortable-ghost {
        opacity: 0.5;
        background: hsl(var(--accent));
        border: 1px dashed hsl(var(--border));
    }

    .bookmark-item.sortable-drag {
        transition: transform 0.2s ease;
        background: hsl(var(--accent));
        color: hsl(var(--accent-foreground));
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    @keyframes bookmark-pulse {
        0% { box-shadow: 0 8px 20px rgba(0,122,255,0.2); }
        50% { box-shadow: 0 8px 30px rgba(0,122,255,0.4); }
        100% { box-shadow: 0 8px 20px rgba(0,122,255,0.2); }
    }

    /* 序号样式 - Shadcn/UI风格 */
    .bookmark-index {
        width: 24px;
        height: 24px;
        background: hsl(var(--muted));
        color: hsl(var(--muted-foreground));
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 500;
        margin-right: 12px;
        flex-shrink: 0;
    }

    .bookmark-content-empty .bookmark-index {
        background: hsl(var(--muted));
        color: hsl(var(--muted-foreground));
    }

    /* 书签信息 */
    .bookmark-info {
        flex: 1;
        min-width: 0;
    }

    .bookmark-title {
        font-size: 14px;
        font-weight: 500;
        color: hsl(var(--foreground));
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .bookmark-url {
        font-size: 12px;
        color: hsl(var(--muted-foreground));
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* 链接样式 */
    .bookmark-link {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    /* 拖拽提示 */
    .drag-hint {
        text-align: center;
        padding: 40px 20px;
        color: #8e8e93;
        font-size: 15px;
    }

    .drag-hint-icon {
        font-size: 48px;
        margin-bottom: 15px;
        opacity: 0.3;
    }

    /* 拖拽占位符 */
    .sortable-placeholder {
        border: 2px dashed #007aff;
        background: rgba(0,123,255,0.05);
        border-radius: 14px;
        margin: 6px 10px;
        transition: all 0.2s;
    }
    
    /* 添加专门的动画类，用于实现平滑过渡 */
    .sortable-animation {
        transition: transform 0.8s cubic-bezier(0.25, 1, 0.5, 1) !important;
    }
    
    /* 确保所有元素都有平滑的位置变化 */
    .nav-groups, .bookmarks-list {
        position: relative;
    }
    
    .nav-group-item, .bookmark-item {
        position: relative;
        z-index: 1;
    }

    /* 隐藏原有的结构 */
    .bookmark-group-container {
        display: none !important;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        #container-bookmakrs {
            flex-direction: column;
        }

        .bookmark-nav {
            width: 100%;
        }
    }
`;
    document.head.appendChild(style);

    // 获取存储的排序数据
    function getStoredOrder(key) {
        const stored = GM_getValue(key);
        return stored ? JSON.parse(stored) : null;
    }

    // 存储排序数据
    function setStoredOrder(key, order) {
        GM_setValue(key, JSON.stringify(order));
    }

    // 初始化
    function init() {
        // 确保SortableJS已加载
        if (typeof Sortable === 'undefined') {
            console.log('Waiting for SortableJS to load...');
            setTimeout(init, 100);
            return;
        }

        const container = document.getElementById('container-bookmakrs');
        if (!container) return;

        // 解析原始数据
        const groups = parseData();
        if (groups.length === 0) return;

        // 应用存储的分组顺序
        const groupOrderKey = 'bookmark_group_order';
        const storedGroupOrder = getStoredOrder(groupOrderKey);
        let orderedGroups = [...groups];

        if (storedGroupOrder && storedGroupOrder.length === groups.length) {
            orderedGroups = storedGroupOrder.map(index => groups[index]).filter(group => group);
        }

        // 创建新的布局结构
        createLayout(container, orderedGroups);

        // 显示第一个分组的内容
        showGroupContent(orderedGroups[0].title, orderedGroups[0].items);
    }

    // 解析原始数据
    function parseData() {
        const containers = document.querySelectorAll('.bookmark-group-container');
        const groups = [];

        containers.forEach((container, index) => {
            const title = container.querySelector('.bookmark-group-title').textContent.trim();
            const items = [];

            container.querySelectorAll('.bookmark-list li a').forEach((link, itemIndex) => {
                items.push({
                    title: link.querySelector('span').textContent.trim(),
                    url: link.href,
                    target: link.target,
                    originalIndex: itemIndex
                });
            });

            groups.push({
                title: title,
                items: items,
                originalIndex: index
            });
        });

        return groups;
    }

    // 创建布局
    function createLayout(container, groups) {
        // 创建左侧导航
        const nav = document.createElement('div');
        nav.className = 'bookmark-nav';
        nav.innerHTML = `
        <div class="nav-title">书签管理</div>
        <ul class="nav-groups" id="nav-groups-list">
            ${groups.map((group, index) => `
                <li class="nav-group-item" data-group="${group.title}" data-original-index="${group.originalIndex}">
                    ${group.title === '工作' ? '<div class="nav-group-icon">💼</div>' : 
                      group.title === '学习' ? '<div class="nav-group-icon">📚</div>' : 
                      group.title === '娱乐' ? '<div class="nav-group-icon">🎮</div>' : 
                      group.title === '新闻' ? '<div class="nav-group-icon">📰</div>' : 
                      `<div class="nav-group-index">${index + 1}</div>`}
                    <div class="nav-group-name">${group.title}</div>
                </li>
            `).join('')}
        </ul>
    `;

        // 创建右侧内容区域
        const content = document.createElement('div');
        content.className = 'bookmark-content';
        content.innerHTML = `
        <div class="content-header">
            <h3 id="current-group-title"></h3>
        </div>
        <div class="search-container">
            <div class="search-input-wrapper">
                <span class="search-icon">🔍</span>
                <input type="text" class="search-input" placeholder="搜索书签..." id="bookmark-search">
            </div>
        </div>
        <div class="bookmarks-list" id="bookmarks-list"></div>
    `;
    
        // 添加搜索功能
        content.querySelector('#bookmark-search').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase().trim();
            const bookmarkItems = document.querySelectorAll('.bookmark-item');
            
            bookmarkItems.forEach(item => {
                const title = item.querySelector('.bookmark-title').textContent.toLowerCase();
                const url = item.querySelector('.bookmark-url').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || url.includes(searchTerm) || searchTerm === '') {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // 清空原容器并添加新结构
        container.innerHTML = '';
        container.appendChild(nav);
        container.appendChild(content);

        // 绑定导航点击事件
        nav.querySelectorAll('.nav-group-item').forEach((item, index) => {
            item.addEventListener('click', function () {
                // 更新激活状态
                nav.querySelectorAll('.nav-group-item').forEach(el => {
                    el.classList.remove('active');
                });
                this.classList.add('active');

                // 显示对应内容
                const groupTitle = this.dataset.group;
                const group = groups.find(g => g.title === groupTitle);
                if (group) {
                    showGroupContent(group.title, group.items);
                }
            });

            // 默认激活第一个
            if (index === 0) {
                item.classList.add('active');
            }
        });

        // 初始化左侧导航拖拽
        initNavSortable(groups);
    }

    // 初始化左侧导航拖拽
    function initNavSortable(groups) {
        const navList = document.getElementById('nav-groups-list');
        
        // 添加一个辅助函数，用于在拖动过程中应用动画
        function applyTransition(item, duration) {
            item.style.transition = `transform ${duration}ms cubic-bezier(0.2, 1, 0.1, 1)`;
        }
        
        // 创建一个样式元素，添加关键的CSS动画规则
        const styleEl = document.createElement('style');
        styleEl.textContent = `
            /* 拖拽时的动画效果 */
            .sortable-list-dragging .nav-group-item:not(.sortable-chosen):not(.sortable-ghost) {
                transition: transform 800ms cubic-bezier(0.2, 1, 0.1, 1) !important;
            }
            .nav-group-item.sortable-chosen {
                transition: none !important;
            }
            .sortable-fallback {
                opacity: 0.7;
                transform: scale(1.02);
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                z-index: 1000;
            }
        `;
        document.head.appendChild(styleEl);

        new Sortable(navList, {
            animation: 800, // 使用内置动画，但设置较长的时间
            easing: "cubic-bezier(0.2, 1, 0.1, 1)", // 更平滑的缓动效果
            delay: 150, // 增加拖拽开始的延迟，防止意外触发
            delayOnTouchOnly: true, // 仅在触摸设备上应用延迟
            touchStartThreshold: 5, // 触发拖拽的阈值
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            filter: '.nav-group-item.active', // 不允许拖拽激活的项
            preventOnFilter: false,
            
            // 关键参数：使用回退模式以获得更好的动画控制
            forceFallback: true, // 强制使用回退选项，提供更一致的拖拽体验
            fallbackClass: "sortable-fallback", // 回退时的类名
            fallbackOnBody: true, // 将克隆的元素附加到body
            
            // 关键参数：这些设置对于实现平滑动画至关重要
            swapThreshold: 0.65, // 交换阈值
            invertSwap: false,   // 不反转交换逻辑
            direction: 'vertical', // 垂直方向排序
            
            // 关键参数：设置为true以实现平滑过渡动画
            sort: true,          // 允许排序
            // 使用移动模式，这对于实现平滑动画很重要
            supportPointer: false, // 禁用指针事件，使用鼠标事件
            
            // 开始拖动前
            onStart: function(evt) {
                // 添加拖拽中的类，用于应用CSS动画
                navList.classList.add('sortable-list-dragging');
                
                // 为所有非拖动元素添加过渡效果
                const items = navList.querySelectorAll('.nav-group-item');
                items.forEach(item => {
                    if (item !== evt.item) {
                        applyTransition(item, 800);
                    }
                });
                
                // 拖动元素不需要过渡效果
                evt.item.style.transition = 'none';
            },
            
            // 拖动过程中
            onMove: function(evt, originalEvent) {
                // 确保所有非拖动元素都有过渡效果
                const items = navList.querySelectorAll('.nav-group-item');
                items.forEach(item => {
                    if (item !== evt.dragged) {
                        applyTransition(item, 800);
                    }
                });
                return true; // 允许移动
            },
            
            // 拖动结束时
            onEnd: function(evt) {
                // 移除拖拽中的类
                navList.classList.remove('sortable-list-dragging');
                
                // 更新序号
                updateNavNumbers();
                
                // 保存新的顺序
                saveNavOrder(groups);
                
                // 为所有元素添加过渡效果
                const items = navList.querySelectorAll('.nav-group-item');
                items.forEach(item => {
                    applyTransition(item, 800);
                });
                
                // 如果拖拽的是当前激活的分组，需要更新右侧内容
                const activeItem = navList.querySelector('.nav-group-item.active');
                if (activeItem) {
                    const groupTitle = activeItem.dataset.group;
                    const group = groups.find(g => g.title === groupTitle);
                    if (group) {
                        showGroupContent(group.title, group.items);
                    }
                }
            }
        });
    }

    // 更新导航序号显示
    function updateNavNumbers() {
        const items = document.querySelectorAll('.nav-group-item');
        items.forEach((item, index) => {
            const indexElement = item.querySelector('.nav-group-index');
            if (indexElement) {
                indexElement.textContent = index + 1;
            }
        });
    }

    // 保存导航顺序
    function saveNavOrder(groups) {
        const currentItems = document.querySelectorAll('.nav-group-item');
        const order = Array.from(currentItems).map(item =>
            parseInt(item.dataset.originalIndex)
        );

        setStoredOrder('bookmark_group_order', order);
    }

    // 显示分组内容
    function showGroupContent(title, originalItems) {
        document.getElementById('current-group-title').textContent = title;

        const list = document.getElementById('bookmarks-list');

        // 获取存储的排序
        let orderedItems = [...originalItems];
        const itemOrderKey = `bookmark_item_order_${title}`;
        const storedOrder = getStoredOrder(itemOrderKey);

        if (storedOrder && storedOrder.length === originalItems.length) {
            // 根据存储的顺序重新排列
            orderedItems = storedOrder.map(index => originalItems[index]).filter(item => item);
        }

        if (orderedItems.length === 0) {
            list.innerHTML = `
            <div class="drag-hint">
                <div class="drag-hint-icon">📭</div>
                <div>该分类暂无书签</div>
            </div>
        `;
            list.className = 'bookmarks-list bookmark-content-empty';
            return;
        }

        list.className = 'bookmarks-list';
        list.innerHTML = orderedItems.map((item, index) => `
        <div class="bookmark-item" data-original-index="${item.originalIndex}">
            <div class="bookmark-index">${index + 1}</div>
            <div class="bookmark-info">
                <div class="bookmark-title">${item.title}</div>
                <div class="bookmark-url">${item.url}</div>
            </div>
            <a href="${item.url}" target="${item.target}" class="bookmark-link" tabindex="-1"></a>
        </div>
    `).join('');

        // 初始化右侧书签拖拽
        initBookmarkSortable(title, originalItems);
    }

    // 初始化右侧书签拖拽
    function initBookmarkSortable(groupName, originalItems) {
        const list = document.getElementById('bookmarks-list');

        // 销毁已存在的Sortable实例
        if (list.sortableInstance) {
            list.sortableInstance.destroy();
        }

        // 创建新的Sortable实例
        const sortable = new Sortable(list, {
            animation: 1000, // 使用更长的动画时间
            easing: "cubic-bezier(0.2, 1, 0.1, 1)", // 更平滑的缓动效果
            delay: 150, // 增加拖拽开始的延迟，防止意外触发
            delayOnTouchOnly: true, // 仅在触摸设备上应用延迟
            touchStartThreshold: 5, // 触发拖拽的阈值
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            
            // 关键参数：这些参数会影响元素如何移动
            forceFallback: false, // 使用原生HTML5拖拽
            fallbackClass: "sortable-fallback",
            fallbackOnBody: true,
            
            // 这些参数对于实现平滑动画至关重要
            swapThreshold: 0.5, // 交换阈值
            invertSwap: false,  // 不反转交换逻辑
            direction: 'vertical', // 垂直方向排序
            
            // 设置为true以使用CSS动画
            setData: function (dataTransfer, dragEl) {
                dataTransfer.setData('Text', dragEl.textContent);
            },
            
            // 开始拖动前
            onStart: function(evt) {
                // 为所有元素添加过渡效果
                const items = list.querySelectorAll('.bookmark-item');
                items.forEach(item => {
                    if (item !== evt.item) {
                        // 为非拖动元素添加过渡效果
                        item.style.transition = 'transform 1s cubic-bezier(0.2, 1, 0.1, 1)';
                    }
                });
                
                // 拖动元素不需要过渡效果
                evt.item.style.transition = 'none';
            },
            
            // 拖动过程中
            onMove: function(evt, originalEvent) {
                return true; // 允许移动
            },
            
            // 拖动结束时
            onEnd: function(evt) {
                // 更新序号
                updateBookmarkNumbers();
                
                // 保存新的顺序
                saveBookmarkOrder(groupName, originalItems);
                
                // 恢复所有元素的默认过渡效果
                setTimeout(() => {
                    const items = list.querySelectorAll('.bookmark-item');
                    items.forEach(item => {
                        item.style.transition = '';
                    });
                }, 1000);
            }
        });

        // 保存实例引用
        list.sortableInstance = sortable;
    }

    // 更新书签序号显示
    function updateBookmarkNumbers() {
        const items = document.querySelectorAll('.bookmark-item');
        items.forEach((item, index) => {
            const indexElement = item.querySelector('.bookmark-index');
            if (indexElement) {
                indexElement.textContent = index + 1;
            }
        });
    }

    // 保存书签顺序
    function saveBookmarkOrder(groupName, originalItems) {
        const currentItems = document.querySelectorAll('.bookmark-item');
        const order = Array.from(currentItems).map(item =>
            parseInt(item.dataset.originalIndex)
        );

        const key = `bookmark_item_order_${groupName}`;
        setStoredOrder(key, order);
    }

    // 添加重置排序菜单项
    GM_registerMenuCommand('重置所有排序', function () {
        if (confirm('确定要重置所有分组和书签的排序吗？')) {
            // 清除所有存储的排序数据
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.startsWith('bookmark_group_order') || key.startsWith('bookmark_item_order_'))) {
                    keysToRemove.push(key);
                }
            }
            keysToRemove.forEach(key => {
                try {
                    GM_setValue(key, null);
                } catch (e) {
                    localStorage.removeItem(key);
                }
            });

            alert('所有排序已重置，请刷新页面');
        }
    });

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();