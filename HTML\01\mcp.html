!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>科技爱好者周刊（第354期）总结</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      background: linear-gradient(135deg, #e3e6ff 0%, #f8fafe 100%);
      font-family: 'Segoe UI', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
      color: #222;
      margin: 0;
      padding: 0;
    }
    header {
      background: #4f7afc;
      color: #fff;
      padding: 2rem 1rem 1rem 1rem;
      text-align: center;
    }
    h1 {
      margin: 0 0 0.2em 0;
      font-size: 2.2em;
      letter-spacing: 2px;
    }
    h2 {
      color: #4f7afc;
      margin-top: 2em;
      margin-bottom: 0.5em;
    }
    .container {
      max-width: 850px;
      margin: 2rem auto;
      background: #fff;
      border-radius: 18px;
      box-shadow: 0 0 24px 0 #e5eafd;
      padding: 2.5rem 2rem 2rem 2rem;
      position: relative;
    }
    .section {
      margin-bottom: 2.2em;
    }
    .toggle-btn {
      background: #4f7afc;
      color: #fff;
      border: none;
      border-radius: 16px;
      padding: 0.4em 1em;
      font-size: 1em;
      cursor: pointer;
      margin-bottom: 1em;
      transition: background 0.2s;
    }
    .toggle-btn:hover {
      background: #3751c7;
    }
    .content {
      display: none;
      animation: fadeIn 0.6s;
    }
    .content.show {
      display: block;
    }
    ul {
      line-height: 1.9;
      margin-left: 1.5em;
    }
    .quote {
      background: #f4f7ff;
      border-left: 4px solid #4f7afc;
      padding: 1em 1.5em;
      margin: 1.2em 0;
      font-style: italic;
      color: #2e3a5d;
      border-radius: 8px;
    }
    .img-cover {
      width: 100%;
      max-width: 520px;
      display: block;
      margin: 1.5em auto;
      border-radius: 14px;
      box-shadow: 0 2px 12px 0 #e3eafc;
    }
    .footer {
      text-align: center;
      margin-top: 3em;
      padding-bottom: 1.5em;
      color: #888;
      font-size: 1em;
    }
    @media (max-width: 600px) {
      .container {
        padding: 1em;
      }
      h1 { font-size: 1.35em; }
      h2 { font-size: 1.1em; }
    }
    @keyframes fadeIn {
      from { opacity: 0;}
      to { opacity: 1;}
    }
  </style>
</head>
<body>
  <header>
    <h1>科技爱好者周刊（第354期）总结</h1>
    <div>2025年6月27日 · 作者：阮一峰</div>
  </header>
  <div class="container">

    <!-- 封面图片 -->
    <img src="https://cdn.beekka.com/blogimg/asset/202506/bg2025061805.webp" alt="成都机器人交警" class="img-cover" />

    <!-- 目录 -->
    <div class="section">
      <button class="toggle-btn" onclick="toggleSection('summary')">📃 总结概览</button>
      <div class="content" id="summary">
        <ul>
          <li>1. 手机电池技术突破，半固态电池量产，续航大幅增强</li>
          <li>2. 世界最长航线开通，东西半球直连</li>
          <li>3. AI 对编程、问答社区、网站流量影响巨大</li>
          <li>4. 各类新工具、资源推荐</li>
          <li>5. 技术圈与社会观察，值得思考的言论</li>
        </ul>
      </div>
    </div>

    <!-- 电池技术突破 -->
    <div class="section">
      <button class="toggle-btn" onclick="toggleSection('battery')">🔋 手机电池革命</button>
      <div class="content" id="battery">
        <h2>8000mAh 手机电池，说明了什么？</h2>
        <ul>
          <li>近年手机电池容量从4000mAh逐步提升到8000mAh，但重量和厚度几乎没变。</li>
          <li>原因：<strong>半固态电池</strong>量产应用，能量密度翻倍。</li>
          <li>半固态电池2023年发布，2024年投产，2025年进入消费电子。</li>
          <li>中国厂商全球领先，三星、苹果暂未跟进。</li>
          <li>续航惊人：8000mAh手机可连续播放25小时视频，2~3天一充。</li>
          <li>固态电池将应用于电动汽车、电动飞机，推动交通工具变革。</li>
        </ul>
        <img src="https://cdn.beekka.com/blogimg/asset/202506/bg2025062502.webp" alt="8000mAh 手机" class="img-cover" />
      </div>
    </div>

    <!-- 科技动态 -->
    <div class="section">
      <button class="toggle-btn" onclick="toggleSection('news')">🌏 科技动态</button>
      <div class="content" id="news">
        <ul>
          <li><b>世界最长航线：</b>中国东航开通中国-阿根廷直航，单程19680公里，中途经新西兰，飞行24-25小时。</li>
          <li><b>AI创业热：</b>以色列一家AI编程公司成立半年被8000万美元收购，AI创业迎来新高潮。</li>
          <li><b>比尔·盖茨与林纳斯·托瓦兹首次会面：</b>历史性合影，Windows与Linux创始人握手言和。</li>
          <li><b>Stack Overflow 被 AI 挤压：</b>2020年峰值后，2025年新问题量降至2008年水平，AI问答替代传统社区。</li>
          <li><b>AI搜索冲击网站流量：</b>研究显示，Google AI摘要让网站访问量下降30%。</li>
        </ul>
      </div>
    </div>

    <!-- 工具与资源 -->
    <div class="section">
      <button class="toggle-btn" onclick="toggleSection('tools')">🛠️ 工具和资源</button>
      <div class="content" id="tools">
        <h2>工具推荐</h2>
        <ul>
          <li>postmarketOS：让旧手机变身Linux设备</li>
          <li>to-userscript：浏览器插件转userscript</li>
          <li>Reeden：本地电子书阅读器</li>
          <li>AdaCpp：在线C++学习环境，带AI讲解</li>
          <li>RingLink：国产远程组网工具</li>
        </ul>
        <h2>AI相关</h2>
        <ul>
          <li>Gemini CLI：谷歌AI命令行客户端</li>
          <li>Twocast：一键生成AI播客，支持多语言</li>
          <li>Duck.ai：DuckDuckGo推隐私型AI聊天</li>
        </ul>
        <h2>资源推荐</h2>
        <ul>
          <li>My Ringtone：免费铃声下载</li>
          <li>维基电台：随机播放维基百科音频</li>
          <li>ICONIC：开源技术图标库</li>
          <li>Linux/Windows开发iOS教程</li>
        </ul>
      </div>
    </div>

    <!-- 观点与文摘 -->
    <div class="section">
      <button class="toggle-btn" onclick="toggleSection('thoughts')">💡 观点与文摘</button>
      <div class="content" id="thoughts">
        <h2>离职面谈建议</h2>
        <div class="quote">
          离职面谈没必要参与，优雅离开比吐槽抱怨更重要。不要留下负面印象，专注未来。
        </div>
        <h2>值得思考的言论</h2>
        <ul>
          <li>AI让90%技能价值归零，但剩下10%暴增1000倍。</li>
          <li>程序员招聘将不再看重语言，解决问题能力最重要。</li>
          <li>社会危机不是孤独，而是无用和隐形。</li>
        </ul>
      </div>
    </div>

    <!-- 互动区 -->
    <div class="section">
      <button class="toggle-btn" onclick="toggleSection('comment')">💬 用户互动（精选留言）</button>
      <div class="content" id="comment">
        <ul>
          <li>比亚迪发不起工资？——实为谣言，原文未提及。</li>
          <li>半固态/固态电池商用令人期待，但安全和寿命仍需观察。</li>
          <li>Stack Overflow下滑，AI替代效应明显。</li>
          <li>外语学习插件引发学习方法讨论。</li>
        </ul>
      </div>
    </div>

    <!-- 尾部 -->
    <div class="footer">
      <div>内容摘自 <a href="http://www.ruanyifeng.com/blog/2025/06/weekly-issue-354.html" target="_blank" style="color:#4f7afc;">阮一峰的网络日志</a></div>
      <div>© 2025 自由转载-署名-非商用-保持一致</div>
    </div>
  </div>
  <script>
    function toggleSection(id) {
      document.querySelectorAll('.content').forEach(function(c) {
        if (c.id === id) {
          c.classList.toggle('show');
        } else {
          c.classList.remove('show');
        }
      });
      window.scrollTo({top: document.getElementById(id).offsetTop - 80, behavior: 'smooth'});
    }
  </script>
</body>
</html>