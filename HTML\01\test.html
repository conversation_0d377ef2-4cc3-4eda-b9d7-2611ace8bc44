<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>行文本点击复制</title>
    <style>
        body {
            font-family: Arial, Helvetica, sans-serif;
            margin: 20px;
        }
        #inputArea {
            width: 100%;
            height: 120px;
            box-sizing: border-box;
            padding: 8px;
            font-size: 14px;
        }
        button {
            margin-top: 8px;
            padding: 6px 12px;
            font-size: 14px;
            cursor: pointer;
        }
        #linesContainer {
            margin-top: 20px;
            border: 1px solid #ddd;
        }
        .line {
            padding: 8px 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            user-select: none; /* 避免双击选中文字影响体验 */
        }
        .line:last-child {
            border-bottom: none;
        }
        .line:hover {
            background-color: #f5f5f5;
        }
        .line.copied {
            background-color: #c8e6c9; /* 复制成功后闪一下绿色 */
        }
        .line.visited {
            color: #1976d2; /* 点击后文字颜色 */
        }
    </style>
</head>
<body>
    <h2>输入多行文字，点击对应行即可复制</h2>

    <textarea id="inputArea" placeholder="在此输入多行文本，每行会单独显示..."></textarea>
    <br>
    <button id="loadBtn">显示</button>

    <div id="linesContainer"></div>

    <script>
        const inputArea = document.getElementById('inputArea');
        const loadBtn = document.getElementById('loadBtn');
        const container = document.getElementById('linesContainer');

        // 创建单行元素
        function createLineElement(text) {
            const div = document.createElement('div');
            div.className = 'line';
            div.textContent = text;

            // 点击复制到剪贴板
            div.addEventListener('click', () => {
                // 使用 Clipboard API
                navigator.clipboard.writeText(text).then(() => {
                    // 视觉反馈
                    div.classList.add('copied');
                    div.classList.add('visited');
                    // 800ms 后移除反馈
                    setTimeout(() => div.classList.remove('copied'), 800);
                }).catch(err => {
                    alert('复制失败: ' + err);
                });
            });

            return div;
        }

        // 根据文本框内容生成行元素
        loadBtn.addEventListener('click', () => {
            container.innerHTML = '';
            const lines = inputArea.value.split(/\r?\n/).filter(l => l.trim() !== '');
            lines.forEach(line => container.appendChild(createLineElement(line)));
        });
    </script>
</body>
</html>