<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 221.2 83.2% 53.3%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 221.2 83.2% 53.3%;
            --radius: 0.5rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1.5rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2.25rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary)) 50%, hsl(var(--accent-foreground)));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: hsl(var(--muted-foreground));
            font-size: 1.125rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 2rem;
        }

        .card {
            background-color: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: calc(var(--radius) + 2px);
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        }

        .card-header {
            padding: 1.5rem 1.5rem 0;
        }

        .card-content {
            padding: 1.5rem;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-description {
            color: hsl(var(--muted-foreground));
            font-size: 0.875rem;
        }

        .upload-area {
            border: 2px dashed hsl(var(--border));
            border-radius: var(--radius);
            padding: 3rem 1.5rem;
            text-align: center;
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            background-color: hsl(var(--muted) / 0.3);
        }

        .upload-area:hover {
            border-color: hsl(var(--primary));
            background-color: hsl(var(--primary) / 0.05);
        }

        .upload-area.dragover {
            border-color: hsl(var(--primary));
            background-color: hsl(var(--primary) / 0.1);
        }

        .upload-icon {
            width: 3rem;
            height: 3rem;
            margin: 0 auto 1rem;
            color: hsl(var(--muted-foreground));
        }

        .file-input {
            display: none;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            border-radius: var(--radius);
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            border: none;
            padding: 0.5rem 1rem;
            height: 2.5rem;
        }

        .btn-primary {
            background-color: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
        }

        .btn-primary:hover {
            background-color: hsl(var(--primary) / 0.9);
        }

        .btn-secondary {
            background-color: hsl(var(--secondary));
            color: hsl(var(--secondary-foreground));
        }

        .btn-secondary:hover {
            background-color: hsl(var(--secondary) / 0.8);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: hsl(var(--foreground));
        }

        .input, .select {
            width: 100%;
            height: 2.5rem;
            padding: 0.5rem 0.75rem;
            border: 1px solid hsl(var(--input));
            border-radius: var(--radius);
            background-color: hsl(var(--background));
            font-size: 0.875rem;
            transition: border-color 0.2s ease-in-out;
        }

        .input:focus, .select:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .separator {
            height: 1px;
            background-color: hsl(var(--border));
            margin: 1.5rem 0;
        }

        .table-container {
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: white;
            resize: both;
            overflow: hidden;
            min-width: 300px;
            min-height: 200px;
            max-width: 100%;
            max-height: 600px;
            position: relative;
            width: 100%;
            height: 400px;
        }

        .table-container::before {
            content: "⋮⋮";
            position: absolute;
            bottom: 2px;
            right: 2px;
            color: hsl(var(--muted-foreground));
            font-size: 12px;
            line-height: 1;
            pointer-events: none;
            z-index: 20;
        }

        .table-wrapper {
            width: 100%;
            height: 100%;
            overflow: auto;
            scrollbar-width: thin;
            scrollbar-color: hsl(var(--muted-foreground)) hsl(var(--muted));
        }

        .table-wrapper::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-wrapper::-webkit-scrollbar-track {
            background: hsl(var(--muted));
            border-radius: 4px;
        }

        .table-wrapper::-webkit-scrollbar-thumb {
            background: hsl(var(--muted-foreground));
            border-radius: 4px;
        }

        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: hsl(var(--foreground));
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
        }

        .data-table th {
            background-color: hsl(var(--muted));
            color: hsl(var(--foreground));
            font-weight: 600;
            padding: 0.5rem 0.75rem;
            text-align: left;
            border-bottom: 1px solid hsl(var(--border));
            border-right: 1px solid hsl(var(--border));
            white-space: nowrap;
            position: sticky;
            top: 0;
            z-index: 10;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .data-table th:last-child {
            border-right: none;
        }

        .data-table td {
            padding: 0.5rem 0.75rem;
            border-bottom: 1px solid hsl(var(--border));
            border-right: 1px solid hsl(var(--border));
            white-space: nowrap;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .data-table td:last-child {
            border-right: none;
        }

        .data-table tr:hover {
            background-color: hsl(var(--muted) / 0.5);
        }

        .data-table tr:nth-child(even) {
            background-color: hsl(var(--muted) / 0.2);
        }

        .alert {
            padding: 1rem;
            border-radius: var(--radius);
            margin-bottom: 1rem;
            border: 1px solid;
        }

        .alert-success {
            background-color: hsl(142 76% 36% / 0.1);
            border-color: hsl(142 76% 36% / 0.2);
            color: hsl(142 76% 36%);
        }

        .alert-error {
            background-color: hsl(var(--destructive) / 0.1);
            border-color: hsl(var(--destructive) / 0.2);
            color: hsl(var(--destructive));
        }

        .hidden {
            display: none;
        }

        .icon {
            width: 1rem;
            height: 1rem;
        }

        .tabs {
            width: 100%;
        }

        .tabs-list {
            display: flex;
            border-bottom: 1px solid hsl(var(--border));
            margin-bottom: 1rem;
        }

        .tabs-trigger {
            padding: 0.5rem 1rem;
            border: none;
            background: none;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease-in-out;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .tabs-trigger.active {
            border-bottom-color: hsl(var(--primary));
            color: hsl(var(--primary));
        }

        .tabs-trigger:hover {
            background-color: hsl(var(--muted) / 0.5);
        }

        .tabs-content {
            display: none;
        }

        .tabs-content.active {
            display: block;
        }

        .saved-configs {
            margin-bottom: 1rem;
        }

        .config-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            margin-bottom: 0.5rem;
            background: hsl(var(--muted) / 0.3);
        }

        .config-name {
            flex: 1;
            font-size: 0.875rem;
            color: hsl(var(--foreground));
        }

        .btn-small {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            height: auto;
        }

        .btn-group {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .save-config-form {
            display: none;
            margin-top: 1rem;
            padding: 1rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--muted) / 0.1);
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .table-info {
            font-size: 0.875rem;
            color: hsl(var(--muted-foreground));
        }

        /* 底部弹出式提醒 */
        .toast-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            pointer-events: none;
        }

        .toast {
            background: white;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            padding: 1rem 1.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            min-width: 300px;
            max-width: 400px;
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
            pointer-events: auto;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast-success {
            border-left: 4px solid hsl(142 76% 36%);
        }

        .toast-error {
            border-left: 4px solid hsl(var(--destructive));
        }

        .toast-icon {
            width: 1.25rem;
            height: 1.25rem;
            flex-shrink: 0;
        }

        .toast-success .toast-icon {
            color: hsl(142 76% 36%);
        }

        .toast-error .toast-icon {
            color: hsl(var(--destructive));
        }

        .toast-content {
            flex: 1;
            font-size: 0.875rem;
            color: hsl(var(--foreground));
        }

        .toast-close {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: calc(var(--radius) / 2);
            color: hsl(var(--muted-foreground));
            transition: background-color 0.2s ease-in-out;
        }

        .toast-close:hover {
            background-color: hsl(var(--muted));
        }

        .toast-close .icon {
            width: 1rem;
            height: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 数据分析工具</h1>
            <p>上传表格文件，进行透视分析和排名展示</p>
        </div>

        <div class="main-content">
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i data-lucide="upload"></i>
                        文件上传
                    </div>
                    <div class="card-description">支持 Excel (.xlsx, .xls) 和 CSV 文件</div>
                </div>
                <div class="card-content">
                    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                        <i data-lucide="file-plus" class="upload-icon"></i>
                        <h3>点击或拖拽上传文件</h3>
                        <p>支持 Excel (.xlsx, .xls) 和 CSV 文件</p>
                        <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls,.csv">
                    </div>



                    <div class="separator"></div>

                    <!-- 透视分析控制 -->
                    <div id="pivotControls" style="display: none;">
                        <div class="card-title">
                            <i data-lucide="pie-chart"></i>
                            透视分析
                        </div>

                        <!-- 保存的配置 -->
                        <div class="saved-configs" id="savedPivotConfigs"></div>

                        <div class="form-group">
                            <label class="label">选择透视列：</label>
                            <select id="pivotColumn" class="select">
                                <option value="">请选择列</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="label">选择数值列：</label>
                            <select id="valueColumn" class="select">
                                <option value="">请选择列</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="label">聚合方式：</label>
                            <select id="aggregateMethod" class="select">
                                <option value="sum">求和</option>
                                <option value="count">计数</option>
                                <option value="avg">平均值</option>
                                <option value="max">最大值</option>
                                <option value="min">最小值</option>
                            </select>
                        </div>

                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="performPivot()">
                                <i data-lucide="play"></i>
                                执行透视分析
                            </button>
                            <button class="btn btn-secondary" onclick="toggleSaveForm('pivot')">
                                <i data-lucide="save"></i>
                                保存配置
                            </button>
                        </div>

                        <!-- 保存配置表单 -->
                        <div class="save-config-form" id="pivotSaveForm">
                            <div class="form-group">
                                <label class="label">配置名称：</label>
                                <input type="text" id="pivotConfigName" class="input" placeholder="输入配置名称">
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-primary btn-small" onclick="saveConfig('pivot')">保存</button>
                                <button class="btn btn-secondary btn-small" onclick="toggleSaveForm('pivot')">取消</button>
                            </div>
                        </div>

                        <div class="separator"></div>
                    </div>

                    <!-- 排名分析控制 -->
                    <div id="rankingControls" style="display: none;">
                        <div class="card-title">
                            <i data-lucide="trending-down"></i>
                            排名分析
                        </div>

                        <!-- 保存的配置 -->
                        <div class="saved-configs" id="savedRankingConfigs"></div>

                        <div class="form-group">
                            <label class="label">选择排序列：</label>
                            <select id="rankColumn" class="select">
                                <option value="">请选择列</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="label">显示后几名：</label>
                            <input type="number" id="bottomCount" value="5" min="1" max="100" class="input">
                        </div>
                        <div class="form-group">
                            <label class="label">排序方式：</label>
                            <select id="sortOrder" class="select">
                                <option value="asc">升序（最小值在前）</option>
                                <option value="desc">降序（最大值在前）</option>
                            </select>
                        </div>

                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="performRanking()">
                                <i data-lucide="play"></i>
                                显示排名
                            </button>
                            <button class="btn btn-secondary" onclick="toggleSaveForm('ranking')">
                                <i data-lucide="save"></i>
                                保存配置
                            </button>
                        </div>

                        <!-- 保存配置表单 -->
                        <div class="save-config-form" id="rankingSaveForm">
                            <div class="form-group">
                                <label class="label">配置名称：</label>
                                <input type="text" id="rankingConfigName" class="input" placeholder="输入配置名称">
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-primary btn-small" onclick="saveConfig('ranking')">保存</button>
                                <button class="btn btn-secondary btn-small" onclick="toggleSaveForm('ranking')">取消</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-content">
                    <div class="tabs">
                        <div class="tabs-list">
                            <button class="tabs-trigger active" onclick="switchTab('preview', this)">
                                <i data-lucide="table"></i>
                                数据预览
                            </button>
                            <button class="tabs-trigger" onclick="switchTab('pivot', this)">
                                <i data-lucide="pie-chart"></i>
                                透视结果
                            </button>
                            <button class="tabs-trigger" onclick="switchTab('ranking', this)">
                                <i data-lucide="trending-down"></i>
                                排名结果
                            </button>
                        </div>

                        <!-- 原始数据预览 -->
                        <div class="tabs-content active" id="dataPreview">
                            <div class="table-header">
                                <div class="card-title">
                                    <i data-lucide="table"></i>
                                    数据预览
                                </div>
                                <div class="table-info" id="dataInfo"></div>
                            </div>
                            <div class="table-container" id="dataTableContainer">
                                <div class="table-wrapper">
                                    <div id="dataTable"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 透视分析结果 -->
                        <div class="tabs-content" id="pivotResult">
                            <div class="table-header">
                                <div class="card-title">
                                    <i data-lucide="pie-chart"></i>
                                    透视分析结果
                                </div>
                                <div class="table-info" id="pivotInfo"></div>
                            </div>
                            <div class="table-container" id="pivotTableContainer">
                                <div class="table-wrapper">
                                    <div id="pivotTable"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 排名分析结果 -->
                        <div class="tabs-content" id="rankingResult">
                            <div class="table-header">
                                <div class="card-title">
                                    <i data-lucide="trending-down"></i>
                                    排名分析结果
                                </div>
                                <div class="table-info" id="rankingInfo"></div>
                            </div>
                            <div class="table-container" id="rankingTableContainer">
                                <div class="table-wrapper">
                                    <div id="rankingTable"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 提醒容器 -->
    <div class="toast-container" id="toastContainer"></div>

    <script>
        let currentData = [];
        let columns = [];
        let savedConfigs = {
            pivot: JSON.parse(localStorage.getItem('pivotConfigs') || '[]'),
            ranking: JSON.parse(localStorage.getItem('rankingConfigs') || '[]')
        };

        // 初始化 Lucide 图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            loadSavedConfigs();
        });

        // 文件上传处理
        document.getElementById('fileInput').addEventListener('change', handleFile);

        // 拖拽上传
        const uploadArea = document.querySelector('.upload-area');
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile({ target: { files: files } });
            }
        });

        // 标签页切换
        function switchTab(tabName, triggerElement = null) {
            // 移除所有活动状态
            document.querySelectorAll('.tabs-trigger').forEach(trigger => {
                trigger.classList.remove('active');
            });
            document.querySelectorAll('.tabs-content').forEach(content => {
                content.classList.remove('active');
            });

            // 激活当前标签
            if (triggerElement) {
                triggerElement.classList.add('active');
            } else {
                // 根据 tabName 找到对应的触发器
                const triggerMap = {
                    'preview': 0,
                    'pivot': 1,
                    'ranking': 2
                };
                const triggers = document.querySelectorAll('.tabs-trigger');
                if (triggers[triggerMap[tabName]]) {
                    triggers[triggerMap[tabName]].classList.add('active');
                }
            }

            // 显示对应内容
            const contentMap = {
                'preview': 'dataPreview',
                'pivot': 'pivotResult',
                'ranking': 'rankingResult'
            };

            const targetContent = document.getElementById(contentMap[tabName]);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        }

        function showStatus(message, type) {
            showToast(message, type);
        }

        // Toast 提醒功能
        function showToast(message, type = 'success') {
            const container = document.getElementById('toastContainer');
            const toastId = 'toast-' + Date.now();

            const iconName = type === 'success' ? 'check-circle' : 'alert-circle';

            const toast = document.createElement('div');
            toast.id = toastId;
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <i data-lucide="${iconName}" class="toast-icon"></i>
                <div class="toast-content">${message}</div>
                <button class="toast-close" onclick="closeToast('${toastId}')">
                    <i data-lucide="x" class="icon"></i>
                </button>
            `;

            container.appendChild(toast);

            // 初始化图标
            lucide.createIcons();

            // 显示动画
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // 自动关闭
            setTimeout(() => {
                closeToast(toastId);
            }, 4000);
        }

        function closeToast(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.classList.remove('show');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }
        }

        // 配置保存和加载功能
        function loadSavedConfigs() {
            displaySavedConfigs('pivot');
            displaySavedConfigs('ranking');
        }

        function displaySavedConfigs(type) {
            const container = document.getElementById(`saved${type.charAt(0).toUpperCase() + type.slice(1)}Configs`);
            const configs = savedConfigs[type];

            if (configs.length === 0) {
                container.innerHTML = '';
                return;
            }

            container.innerHTML = configs.map((config, index) => `
                <div class="config-item">
                    <span class="config-name">${config.name}</span>
                    <button class="btn btn-primary btn-small" onclick="loadConfig('${type}', ${index})">
                        <i data-lucide="play"></i>
                        运行
                    </button>
                    <button class="btn btn-secondary btn-small" onclick="deleteConfig('${type}', ${index})">
                        <i data-lucide="trash-2"></i>
                        删除
                    </button>
                </div>
            `).join('');

            // 重新初始化图标
            lucide.createIcons();
        }

        function toggleSaveForm(type) {
            const form = document.getElementById(`${type}SaveForm`);
            const isVisible = form.style.display !== 'none';
            form.style.display = isVisible ? 'none' : 'block';

            if (!isVisible) {
                document.getElementById(`${type}ConfigName`).focus();
            }
        }

        function saveConfig(type) {
            const nameInput = document.getElementById(`${type}ConfigName`);
            const name = nameInput.value.trim();

            if (!name) {
                showStatus('请输入配置名称', 'error');
                return;
            }

            let config = { name };

            if (type === 'pivot') {
                const pivotCol = document.getElementById('pivotColumn').value;
                const valueCol = document.getElementById('valueColumn').value;
                const aggregateMethod = document.getElementById('aggregateMethod').value;

                if (!pivotCol || !valueCol) {
                    showStatus('请先选择透视列和数值列', 'error');
                    return;
                }

                config.pivotColumn = pivotCol;
                config.valueColumn = valueCol;
                config.aggregateMethod = aggregateMethod;
            } else if (type === 'ranking') {
                const rankCol = document.getElementById('rankColumn').value;
                const bottomCount = document.getElementById('bottomCount').value;
                const sortOrder = document.getElementById('sortOrder').value;

                if (!rankCol) {
                    showStatus('请先选择排序列', 'error');
                    return;
                }

                config.rankColumn = rankCol;
                config.bottomCount = bottomCount;
                config.sortOrder = sortOrder;
            }

            savedConfigs[type].push(config);
            localStorage.setItem(`${type}Configs`, JSON.stringify(savedConfigs[type]));

            displaySavedConfigs(type);
            toggleSaveForm(type);
            nameInput.value = '';

            showStatus('配置保存成功！', 'success');
        }

        function loadConfig(type, index) {
            const config = savedConfigs[type][index];

            if (type === 'pivot') {
                document.getElementById('pivotColumn').value = config.pivotColumn;
                document.getElementById('valueColumn').value = config.valueColumn;
                document.getElementById('aggregateMethod').value = config.aggregateMethod;
                performPivot();
            } else if (type === 'ranking') {
                document.getElementById('rankColumn').value = config.rankColumn;
                document.getElementById('bottomCount').value = config.bottomCount;
                document.getElementById('sortOrder').value = config.sortOrder;
                performRanking();
            }
        }

        function deleteConfig(type, index) {
            if (confirm('确定要删除这个配置吗？')) {
                savedConfigs[type].splice(index, 1);
                localStorage.setItem(`${type}Configs`, JSON.stringify(savedConfigs[type]));
                displaySavedConfigs(type);
                showStatus('配置删除成功！', 'success');
            }
        }

        function handleFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            showStatus('正在读取文件...', 'success');

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    let data;
                    if (file.name.endsWith('.csv')) {
                        data = parseCSV(e.target.result);
                    } else {
                        const workbook = XLSX.read(e.target.result, { type: 'binary' });
                        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                        data = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
                    }

                    if (data.length === 0) {
                        showStatus('文件为空或格式不正确', 'error');
                        return;
                    }

                    currentData = data;
                    columns = data[0];
                    
                    displayData();
                    setupControls();
                    showStatus('文件上传成功！', 'success');
                } catch (error) {
                    showStatus('文件读取失败：' + error.message, 'error');
                }
            };

            if (file.name.endsWith('.csv')) {
                reader.readAsText(file);
            } else {
                reader.readAsBinaryString(file);
            }
        }

        function parseCSV(text) {
            const lines = text.split('\n');
            return lines.map(line => {
                const result = [];
                let current = '';
                let inQuotes = false;
                
                for (let i = 0; i < line.length; i++) {
                    const char = line[i];
                    if (char === '"') {
                        inQuotes = !inQuotes;
                    } else if (char === ',' && !inQuotes) {
                        result.push(current.trim());
                        current = '';
                    } else {
                        current += char;
                    }
                }
                result.push(current.trim());
                return result;
            }).filter(row => row.some(cell => cell !== ''));
        }

        function displayData() {
            const previewData = currentData.slice(0, 11); // 显示前10行数据
            const tableHtml = createTable(previewData);
            document.getElementById('dataTable').innerHTML = tableHtml;

            // 显示表格信息
            const totalRows = currentData.length - 1; // 减去表头
            const totalCols = columns.length;
            document.getElementById('dataInfo').textContent = `共 ${totalRows} 行 ${totalCols} 列 (显示前10行)`;

            // 切换到数据预览标签
            switchTab('preview');
        }

        function setupControls() {
            const pivotColumn = document.getElementById('pivotColumn');
            const valueColumn = document.getElementById('valueColumn');
            const rankColumn = document.getElementById('rankColumn');

            // 清空选项
            [pivotColumn, valueColumn, rankColumn].forEach(select => {
                select.innerHTML = '<option value="">请选择列</option>';
            });

            // 添加列选项
            columns.forEach((col, index) => {
                const option1 = new Option(col, index);
                const option2 = new Option(col, index);
                const option3 = new Option(col, index);

                pivotColumn.appendChild(option1);
                valueColumn.appendChild(option2);
                rankColumn.appendChild(option3);
            });

            document.getElementById('pivotControls').style.display = 'block';
            document.getElementById('rankingControls').style.display = 'block';
        }

        function createTable(data) {
            if (!data || data.length === 0) return '<p>无数据</p>';

            let html = '<table class="data-table"><thead><tr>';
            
            // 表头
            data[0].forEach(header => {
                html += `<th>${header}</th>`;
            });
            html += '</tr></thead><tbody>';

            // 数据行
            for (let i = 1; i < data.length; i++) {
                html += '<tr>';
                data[i].forEach(cell => {
                    html += `<td>${cell || ''}</td>`;
                });
                html += '</tr>';
            }

            html += '</tbody></table>';
            return html;
        }

        function performPivot() {
            const pivotColIndex = document.getElementById('pivotColumn').value;
            const valueColIndex = document.getElementById('valueColumn').value;
            const aggregateMethod = document.getElementById('aggregateMethod').value;

            if (pivotColIndex === '' || valueColIndex === '') {
                showStatus('请选择透视列和数值列', 'error');
                return;
            }

            const pivotData = {};
            
            // 跳过表头，从第二行开始处理数据
            for (let i = 1; i < currentData.length; i++) {
                const row = currentData[i];
                const pivotKey = row[pivotColIndex];
                const value = parseFloat(row[valueColIndex]) || 0;

                if (!pivotData[pivotKey]) {
                    pivotData[pivotKey] = [];
                }
                pivotData[pivotKey].push(value);
            }

            // 计算聚合值
            const result = [];
            result.push([columns[pivotColIndex], `${aggregateMethod}(${columns[valueColIndex]})`]);

            Object.keys(pivotData).forEach(key => {
                const values = pivotData[key];
                let aggregatedValue;

                switch (aggregateMethod) {
                    case 'sum':
                        aggregatedValue = values.reduce((a, b) => a + b, 0);
                        break;
                    case 'count':
                        aggregatedValue = values.length;
                        break;
                    case 'avg':
                        aggregatedValue = values.reduce((a, b) => a + b, 0) / values.length;
                        break;
                    case 'max':
                        aggregatedValue = Math.max(...values);
                        break;
                    case 'min':
                        aggregatedValue = Math.min(...values);
                        break;
                }

                result.push([key, aggregatedValue.toFixed(2)]);
            });

            const tableHtml = createTable(result);
            document.getElementById('pivotTable').innerHTML = tableHtml;

            // 显示表格信息
            const pivotRows = result.length - 1; // 减去表头
            const pivotCols = result[0].length;
            document.getElementById('pivotInfo').textContent = `透视结果: ${pivotRows} 行 ${pivotCols} 列`;

            // 切换到透视结果标签
            document.querySelectorAll('.tabs-trigger').forEach(trigger => {
                trigger.classList.remove('active');
            });
            document.querySelectorAll('.tabs-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelector('.tabs-trigger:nth-child(2)').classList.add('active');
            document.getElementById('pivotResult').classList.add('active');

            showStatus('透视分析完成！', 'success');
        }

        function performRanking() {
            const rankColIndex = document.getElementById('rankColumn').value;
            const bottomCount = parseInt(document.getElementById('bottomCount').value);
            const sortOrder = document.getElementById('sortOrder').value;

            if (rankColIndex === '') {
                showStatus('请选择排序列', 'error');
                return;
            }

            // 准备数据（跳过表头）
            const dataWithIndex = currentData.slice(1).map((row, index) => ({
                originalIndex: index + 1,
                row: row,
                sortValue: parseFloat(row[rankColIndex]) || 0
            }));

            // 排序
            dataWithIndex.sort((a, b) => {
                if (sortOrder === 'asc') {
                    return a.sortValue - b.sortValue;
                } else {
                    return b.sortValue - a.sortValue;
                }
            });

            // 取后几名
            const bottomData = dataWithIndex.slice(-bottomCount);
            
            // 构建结果表格
            const result = [];
            result.push([...columns, '排名']);

            bottomData.forEach((item, index) => {
                const rank = dataWithIndex.length - bottomCount + index + 1;
                result.push([...item.row, rank]);
            });

            const tableHtml = createTable(result);
            document.getElementById('rankingTable').innerHTML = tableHtml;

            // 显示表格信息
            const rankingRows = result.length - 1; // 减去表头
            const rankingCols = result[0].length;
            document.getElementById('rankingInfo').textContent = `排名结果: 后${bottomCount}名，共 ${rankingRows} 行 ${rankingCols} 列`;

            // 切换到排名结果标签
            document.querySelectorAll('.tabs-trigger').forEach(trigger => {
                trigger.classList.remove('active');
            });
            document.querySelectorAll('.tabs-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelector('.tabs-trigger:nth-child(3)').classList.add('active');
            document.getElementById('rankingResult').classList.add('active');

            showStatus(`已显示后${bottomCount}名数据！`, 'success');
        }
    </script>
</body>
</html>
