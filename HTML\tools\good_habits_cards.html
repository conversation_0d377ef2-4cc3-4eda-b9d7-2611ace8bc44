<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小朋友的十个好习惯</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .page {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto 30px;
            background: white;
            padding: 25mm;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border-radius: 15px;
            page-break-after: always;
        }
        
        .page:last-child {
            margin-bottom: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            border-radius: 20px;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header h1 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
        }

        .header p {
            font-size: 20px;
            font-weight: 400;
            line-height: 1.6;
        }
        
        .habits-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .habit-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
            border-radius: 15px;
            padding: 20px;
            color: #333;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(102, 126, 234, 0.5);
        }

        .habit-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
            transform: rotate(45deg);
            transition: all 0.5s;
        }

        .habit-card:hover::before {
            animation: shine 0.5s ease-in-out;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .habit-card:nth-child(odd) {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.25) 0%, rgba(254, 202, 87, 0.25) 100%);
            border-color: rgba(255, 107, 107, 0.4);
        }

        .habit-card:nth-child(even) {
            background: linear-gradient(135deg, rgba(72, 219, 251, 0.25) 0%, rgba(10, 189, 227, 0.25) 100%);
            border-color: rgba(72, 219, 251, 0.4);
        }

        .habit-card:nth-child(3n) {
            background: linear-gradient(135deg, rgba(255, 159, 243, 0.25) 0%, rgba(243, 104, 224, 0.25) 100%);
            border-color: rgba(255, 159, 243, 0.4);
        }

        .habit-card:nth-child(4n) {
            background: linear-gradient(135deg, rgba(84, 160, 255, 0.25) 0%, rgba(46, 134, 222, 0.25) 100%);
            border-color: rgba(84, 160, 255, 0.4);
        }

        .habit-card:nth-child(5n) {
            background: linear-gradient(135deg, rgba(95, 39, 205, 0.25) 0%, rgba(52, 31, 151, 0.25) 100%);
            border-color: rgba(95, 39, 205, 0.4);
        }
        
        .habit-number {
            display: inline-block;
            width: 45px;
            height: 45px;
            background: rgba(255,255,255,0.8);
            border-radius: 50%;
            text-align: center;
            line-height: 45px;
            font-weight: 700;
            font-size: 20px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
            color: #333;
            border: 2px solid rgba(0,0,0,0.1);
        }

        .habit-title {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #2c3e50;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
        }

        .habit-content {
            font-size: 18px;
            line-height: 1.6;
            font-weight: 400;
            color: #34495e;
        }
        
        .footer {
            text-align: center;
            padding: 25px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border-radius: 20px;
            color: white;
            margin-top: 30px;
        }
        
        .footer h2 {
            font-size: 26px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .footer p {
            font-size: 20px;
            font-weight: 400;
        }
        
        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }
        
        .star {
            position: absolute;
            color: rgba(255,255,255,0.6);
            font-size: 20px;
            animation: twinkle 2s infinite;
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .export-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            z-index: 1000;
            font-family: 'Noto Sans SC', sans-serif;
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .export-btn:active {
            transform: translateY(0);
        }

        @media print {
            body {
                background: white !important;
                padding: 0 !important;
            }

            .page {
                box-shadow: none !important;
                border-radius: 0 !important;
                margin: 0 !important;
                padding: 15mm !important;
                background: white !important;
            }

            .habit-card {
                background: rgba(255, 107, 107, 0.3) !important;
                border: 2px solid rgba(255, 107, 107, 0.5) !important;
                color: #333 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .habit-card:nth-child(odd) {
                background: rgba(255, 107, 107, 0.3) !important;
                border-color: rgba(255, 107, 107, 0.5) !important;
            }

            .habit-card:nth-child(even) {
                background: rgba(72, 219, 251, 0.3) !important;
                border-color: rgba(72, 219, 251, 0.5) !important;
            }

            .habit-card:nth-child(3n) {
                background: rgba(255, 159, 243, 0.3) !important;
                border-color: rgba(255, 159, 243, 0.5) !important;
            }

            .habit-card:nth-child(4n) {
                background: rgba(84, 160, 255, 0.3) !important;
                border-color: rgba(84, 160, 255, 0.5) !important;
            }

            .habit-card:nth-child(5n) {
                background: rgba(95, 39, 205, 0.3) !important;
                border-color: rgba(95, 39, 205, 0.5) !important;
            }

            .habit-number {
                background: rgba(255,255,255,0.9) !important;
                color: #333 !important;
                border: 2px solid rgba(0,0,0,0.2) !important;
            }

            .habit-title {
                color: #2c3e50 !important;
                text-shadow: none !important;
            }

            .habit-content {
                color: #34495e !important;
            }

            .header {
                background: linear-gradient(45deg, #ff6b6b, #feca57) !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .footer {
                background: linear-gradient(45deg, #ff6b6b, #feca57) !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .habit-card:hover::before {
                animation: none !important;
            }

            .export-btn, .export-container {
                display: none !important;
            }

            .decorative-elements {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- 导出按钮 -->
    <div class="export-container" style="position: fixed; top: 20px; right: 20px; z-index: 1000;">
        <button class="export-btn" onclick="exportAsImage()" style="margin-bottom: 10px; display: block; width: 120px;">�️ 导出图片</button>
        <button class="export-btn" onclick="printPage()" style="background: linear-gradient(45deg, #48dbfb, #0abde3); display: block; width: 120px;">🖨️ 打印</button>
    </div>
    <!-- 第一页 -->
    <div class="page">
        <div class="decorative-elements">
            <div class="star" style="top: 10%; left: 15%;">⭐</div>
            <div class="star" style="top: 20%; right: 10%;">✨</div>
            <div class="star" style="top: 60%; left: 5%;">🌟</div>
            <div class="star" style="bottom: 20%; right: 20%;">⭐</div>
        </div>
        
        <div class="header">
            <h1>🌈 小朋友的十个好习惯 🌈</h1>
            <p>这些习惯越早养成越好哦！养好了，长大后会让我们受益良多呢～</p>
        </div>
        
        <div class="habits-grid">
            <div class="habit-card">
                <div class="habit-number">1</div>
                <div class="habit-title">🧸 自己的事情自己做</div>
                <div class="habit-content">刷牙、洗脸、穿衣服、叠被子……只要是和自己有关的事，都试着自己完成。这样长大以后，我们就不会依赖别人啦～</div>
            </div>
            
            <div class="habit-card">
                <div class="habit-number">2</div>
                <div class="habit-title">🏠 家里的事情帮着做</div>
                <div class="habit-content">我很小的时候，就会帮爸爸妈妈打扫卫生，比如擦桌子、扫地、拖地。因为我是家里的一员，当然要帮爸爸妈妈分担呀～</div>
            </div>
            
            <div class="habit-card">
                <div class="habit-number">3</div>
                <div class="habit-title">📚 用过的东西放回原位</div>
                <div class="habit-content">以前我总乱放东西，想找彩色笔或胶棒时，怎么都找不到。后来妈妈告诉我，用过的东西放回原处，下次就能在同一个地方轻松找到啦～</div>
            </div>
            
            <div class="habit-card">
                <div class="habit-number">4</div>
                <div class="habit-title">💬 学会表达，不乱发脾气</div>
                <div class="habit-content">哭是解决不了问题的，有话要好好说。每次我闹脾气后，妈妈都会抱着我解释：心情不好可以哭，但不能无理取闹，有问题要勇敢说出来～</div>
            </div>
            
            <div class="habit-card">
                <div class="habit-number">5</div>
                <div class="habit-title">👋 懂礼貌，主动打招呼</div>
                <div class="habit-content">我们不跟陌生人随意说话，但遇到认识的邻居、爷爷奶奶、叔叔阿姨和老师，要主动问好呀～</div>
            </div>
        </div>
    </div>

    <!-- 第二页 -->
    <div class="page">
        <div class="decorative-elements">
            <div class="star" style="top: 15%; left: 10%;">🌟</div>
            <div class="star" style="top: 25%; right: 15%;">⭐</div>
            <div class="star" style="top: 70%; left: 8%;">✨</div>
            <div class="star" style="bottom: 15%; right: 12%;">🌟</div>
        </div>

        <div class="habits-grid">
            <div class="habit-card">
                <div class="habit-number">6</div>
                <div class="habit-title">😴 学会自律，早睡早起</div>
                <div class="habit-content">我们正在长身体，睡眠不够就会睡不醒。有一次我想多玩一会儿，睡得很晚，早上起不来，上学迟到了，还错过了晨间活动，这就是"恶性循环"哦～</div>
            </div>

            <div class="habit-card">
                <div class="habit-number">7</div>
                <div class="habit-title">🍎 自己吃饭不挑食</div>
                <div class="habit-content">有的小朋友这也不吃、那也不吃，这样营养会不均衡呢！我们长身体时，要吃五谷杂粮和蔬菜，吃饭也别磨蹭、别边吃边玩。吃完后，记得把碗收到厨房哦～</div>
            </div>

            <div class="habit-card">
                <div class="habit-number">8</div>
                <div class="habit-title">👀 少看电视和手机</div>
                <div class="habit-content">妈妈常说，她小时候看电视太多，现在不戴眼镜看东西都是模糊的。我们要保护眼睛，动画片每天看10-20分钟就好啦～</div>
            </div>

            <div class="habit-card">
                <div class="habit-number">9</div>
                <div class="habit-title">🤐 不说脏话，不大喊大叫</div>
                <div class="habit-content">我们家不说脏话，有一次爸爸习惯说"我K"，我都提醒他啦～在公共场合大喊大叫也不好，这是做人的基本礼貌呀～</div>
            </div>

            <div class="habit-card">
                <div class="habit-number">10</div>
                <div class="habit-title">❤️ 经常表达关心和爱</div>
                <div class="habit-content">早上妈妈会说"早上好"，晚上说"晚安"；爸爸起床后会抱抱我们、亲亲我们。慢慢我就学会关心别人啦～有一次妈妈不舒服，我还倒了杯45°的温水给她呢～</div>
            </div>
        </div>

        <div class="footer">
            <h2>🎉 小朋友们，这些习惯你们记住了吗？</h2>
            <p>快从现在开始试试吧～ 让我们一起成为更棒的自己！</p>
        </div>
    </div>

    <script>
        async function exportAsImage() {
            const btn = document.querySelector('.export-btn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '⏳ 生成中...';
            btn.disabled = true;

            try {
                // 临时隐藏按钮容器
                const exportContainer = document.querySelector('.export-container');
                exportContainer.style.display = 'none';

                // 获取所有页面
                const pages = document.querySelectorAll('.page');

                for (let i = 0; i < pages.length; i++) {
                    const page = pages[i];

                    // 为每个页面生成图片
                    const canvas = await html2canvas(page, {
                        scale: 2,
                        useCORS: true,
                        allowTaint: false,
                        backgroundColor: '#ffffff',
                        width: page.offsetWidth,
                        height: page.offsetHeight,
                        scrollX: 0,
                        scrollY: 0,
                        windowWidth: window.innerWidth,
                        windowHeight: window.innerHeight
                    });

                    // 创建下载链接
                    const link = document.createElement('a');
                    link.download = `小朋友的十个好习惯_第${i + 1}页.png`;
                    link.href = canvas.toDataURL('image/png', 1.0);

                    // 触发下载
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // 添加延迟避免浏览器阻止多个下载
                    if (i < pages.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                }

                // 恢复按钮容器
                exportContainer.style.display = '';
                btn.innerHTML = originalText;
                btn.disabled = false;

                showNotification(`成功导出 ${pages.length} 张图片！`, 'success');

            } catch (error) {
                console.error('图片导出失败:', error);

                // 恢复按钮容器
                const exportContainer = document.querySelector('.export-container');
                exportContainer.style.display = '';
                btn.innerHTML = originalText;
                btn.disabled = false;

                showNotification('图片导出失败，请重试', 'error');
            }
        }

        function printPage() {
            // 使用浏览器打印功能，更稳定
            window.print();
        }

        function showNotification(message, type) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : '#f44336'};
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 1001;
                font-family: 'Noto Sans SC', sans-serif;
                font-size: 14px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);

            // 3秒后自动消失
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
