# 余额查询工具使用说明

## 功能概述

这是一个网页余额查询工具，可以实现真实的网站余额查询。通过代理服务器解决浏览器CORS限制，支持多网站、多账号管理。

## 文件结构

```
HTML/查询余额/
├── test.html           # 主页面文件
├── proxy-server.py     # Python代理服务器
├── requirements.txt    # Python依赖包
├── start-server.bat    # Windows启动脚本
└── README.md          # 使用说明
```

## 快速开始

### 1. 启动代理服务器

**Windows用户：**
双击运行 `start-server.bat` 文件，会自动安装依赖并启动服务器。

**手动启动：**
```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务器
python proxy-server.py
```

服务器启动后会显示：
```
启动余额查询代理服务...
服务地址: http://localhost:5000
健康检查: http://localhost:5000/health
```

### 2. 打开网页工具

用浏览器打开 `test.html` 文件即可使用。

## 使用流程

### 首次使用
1. 页面会自动创建一个Sophnet的默认账号
2. 选择"Sophnet"网站
3. 选择"主账号"
4. 点击"查询余额"

### 添加新账号
1. 点击"管理设置"按钮
2. 在"添加账号"标签页填写信息：
   - 网站名称：自定义名称
   - 账号名称：账号标识
   - 查询网址：目标页面URL
   - Cookies：完整的cookie字符串
   - XPath路径：余额元素的XPath

### 管理现有账号
1. 在"管理账号"标签页可以：
   - 查看所有保存的账号
   - 编辑账号信息
   - 删除不需要的账号

## 技术原理

### 架构说明
```
浏览器 → 代理服务器 → 目标网站
   ↑         ↓
   ←─── 解析结果 ←───
```

1. **前端页面**：提供用户界面和账号管理
2. **代理服务器**：使用Python Flask处理跨域请求
3. **数据解析**：使用lxml和XPath提取页面数据

### 核心功能

**代理服务器 (proxy-server.py)：**
- 接收前端POST请求
- 使用cookies访问目标网站
- 通过XPath解析页面元素
- 提取余额信息并返回JSON

**前端页面 (test.html)：**
- 账号信息本地存储
- 选择式查询界面
- 实时状态反馈
- 响应式设计

## 获取必要信息

### 1. 获取Cookies
1. 打开浏览器开发者工具 (F12)
2. 登录目标网站
3. 在Network标签页找到页面请求
4. 复制Request Headers中的Cookie值

### 2. 获取XPath
1. 在目标页面右键点击余额元素
2. 选择"检查元素"
3. 在Elements面板中右键该元素
4. 选择 Copy → Copy XPath

### 3. 测试XPath
可以在浏览器控制台中测试：
```javascript
$x('/html/body/div[1]/div[1]/div[2]/div/div[2]/section/div/div/div[1]/div/div/div/div[1]/div[2]')
```

## 故障排除

### 常见问题

**1. 查询失败：连接被拒绝**
- 确保代理服务器已启动
- 检查服务器地址是否为 http://localhost:5000

**2. 查询失败：未找到元素**
- 检查XPath路径是否正确
- 确认目标页面结构是否发生变化

**3. 查询失败：认证失败**
- 检查Cookies是否过期
- 重新获取最新的Cookie信息

**4. Python依赖安装失败**
- 确保已安装Python 3.6+
- 使用管理员权限运行命令

### 调试方法

**查看服务器日志：**
代理服务器会在控制台显示详细的请求和错误信息。

**查看浏览器控制台：**
按F12打开开发者工具，查看Console标签页的错误信息。

**测试服务器健康状态：**
访问 http://localhost:5000/health 检查服务器是否正常运行。

## 安全注意事项

1. **Cookie安全**：Cookies包含敏感信息，请妥善保管
2. **本地存储**：账号信息存储在浏览器本地，清除浏览器数据会丢失
3. **网络安全**：代理服务器仅在本地运行，不会泄露信息到外部

## 扩展功能

### 添加新网站支持
1. 在"添加账号"中填写新网站信息
2. 根据网站页面结构调整XPath
3. 如需特殊解析逻辑，可修改 `parse_balance_text` 函数

### 自定义解析规则
在 `proxy-server.py` 中的 `parse_balance_text` 函数可以添加针对不同网站的解析逻辑：

```python
def parse_balance_text(text):
    # 根据网站特征添加不同的解析规则
    if '特定网站标识' in text:
        # 自定义解析逻辑
        pass
    
    # 通用解析逻辑
    # ...
```

## 版本信息

- 版本：1.0.0
- 更新日期：2025-01-16
- 支持平台：Windows, macOS, Linux
- 浏览器要求：Chrome 60+, Firefox 55+, Safari 12+

## 技术支持

如遇到问题，请检查：
1. Python环境是否正确安装
2. 依赖包是否完整安装
3. 网络连接是否正常
4. 目标网站是否可正常访问