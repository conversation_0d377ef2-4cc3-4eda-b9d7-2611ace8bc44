#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
余额查询代理服务器
解决浏览器CORS限制，实现真实网页查询
支持多网站自定义解析逻辑
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import requests
from bs4 import BeautifulSoup
import re
import json
import time
from urllib.parse import urlparse

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 网站解析器配置
SITE_PARSERS = {
    'sophnet': {
        'domain': 'sophnet.com',
        'parser': 'parse_sophnet_balance'
    }
}

@app.route('/query', methods=['POST'])
def query_balance():
    try:
        data = request.json
        url = data.get('url')
        cookies_str = data.get('cookies')
        xpath = data.get('xpath')
        site_name = data.get('siteName', '').lower()
        
        if not all([url, cookies_str]):
            return jsonify({'error': '缺少必要参数'}), 400
        
        print(f"开始查询: {url}")
        print(f"网站名称: {site_name}")
        
        # 解析cookies字符串为字典
        cookies = parse_cookies(cookies_str)
        
        # 设置请求头
        headers = get_request_headers(url)
        
        # 发送请求
        print("发送HTTP请求...")
        response = requests.get(url, cookies=cookies, headers=headers, timeout=30)
        response.raise_for_status()
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容长度: {len(response.content)}")
        
        # 根据网站类型选择解析方法
        domain = urlparse(url).netloc.lower()
        parser_info = get_site_parser(domain, site_name)
        
        if parser_info:
            print(f"使用专用解析器: {parser_info['parser']}")
            balance_info = globals()[parser_info['parser']](response.content, xpath)
        else:
            print("使用通用解析器")
            balance_info = parse_generic_balance(response.content, xpath)
        
        return jsonify({
            'success': True,
            'balance': balance_info['balance'],
            'raw_text': balance_info['raw_text'],
            'parser_used': parser_info['parser'] if parser_info else 'generic'
        })
        
    except requests.RequestException as e:
        error_msg = f'网络请求失败: {str(e)}'
        print(f"错误: {error_msg}")
        return jsonify({'error': error_msg}), 500
    except Exception as e:
        error_msg = f'解析失败: {str(e)}'
        print(f"错误: {error_msg}")
        return jsonify({'error': error_msg}), 500

def get_request_headers(url):
    """根据URL生成请求头"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0'
    }
    
    # 根据不同网站添加特定头部
    if 'sophnet.com' in url:
        headers.update({
            'Referer': 'https://www.sophnet.com/',
            'Origin': 'https://www.sophnet.com'
        })
    
    return headers

def get_site_parser(domain, site_name):
    """根据域名或网站名称获取解析器信息"""
    # 优先根据网站名称匹配
    if site_name in SITE_PARSERS:
        return SITE_PARSERS[site_name]
    
    # 根据域名匹配
    for parser_name, parser_info in SITE_PARSERS.items():
        if parser_info['domain'] in domain:
            return parser_info
    
    return None

def parse_cookies(cookies_str):
    """解析cookies字符串为字典"""
    cookies = {}
    for item in cookies_str.split(';'):
        if '=' in item:
            key, value = item.strip().split('=', 1)
            cookies[key] = value
    return cookies

def parse_sophnet_balance(html_content, xpath=None):
    """Sophnet网站专用余额解析器"""
    try:
        print("使用Sophnet专用解析器")
        
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 保存HTML到文件用于调试
        with open('debug_sophnet.html', 'w', encoding='utf-8') as f:
            f.write(soup.prettify())
        print("HTML内容已保存到 debug_sophnet.html")
        
        # 多种方法查找余额信息
        balance_div = None
        raw_text = ""
        
        # 方法1: 查找特定class的div
        balance_div = soup.find('div', class_='flex items-center mt-3')
        if balance_div:
            print("方法1成功: 找到 flex items-center mt-3 div")
        
        # 方法2: 查找包含"余额"文本的元素
        if not balance_div:
            print("方法1失败，尝试方法2...")
            balance_elements = soup.find_all(string=re.compile(r'余额'))
            print(f"找到 {len(balance_elements)} 个包含'余额'的文本元素")
            
            for i, elem in enumerate(balance_elements):
                print(f"余额元素 {i+1}: {elem.strip()}")
                parent = elem.parent
                if parent:
                    parent_text = parent.get_text(strip=True)
                    print(f"父元素文本: {parent_text}")
                    if '￥' in parent_text:
                        balance_div = parent
                        print("方法2成功: 通过父元素找到余额")
                        break
        
        # 方法3: 查找包含货币符号的元素
        if not balance_div:
            print("方法2失败，尝试方法3...")
            money_elements = soup.find_all(string=re.compile(r'￥\d+'))
            print(f"找到 {len(money_elements)} 个包含货币符号的元素")
            
            for i, elem in enumerate(money_elements):
                print(f"货币元素 {i+1}: {elem.strip()}")
                # 向上查找包含完整余额信息的容器
                current = elem.parent
                for level in range(5):  # 最多向上查找5层
                    if current:
                        text = current.get_text(strip=True)
                        if '余额' in text and '充值' in text and '赠送' in text:
                            balance_div = current
                            print(f"方法3成功: 在第{level+1}层找到完整余额信息")
                            break
                        current = current.parent
                    else:
                        break
                if balance_div:
                    break
        
        # 方法4: 搜索整个页面文本
        if not balance_div:
            print("方法3失败，尝试方法4...")
            page_text = soup.get_text()
            balance_match = re.search(r'余额[：:][^￥]*￥\d+[^）]*（[^）]*充值余额[^）]*￥\d+[^）]*赠送余额[^）]*￥\d+[^）]*）', page_text)
            if balance_match:
                raw_text = balance_match.group(0)
                print(f"方法4成功: 通过正则表达式找到余额信息")
            else:
                print("方法4失败")
        
        # 获取文本内容
        if balance_div and not raw_text:
            raw_text = balance_div.get_text(strip=True)
        
        if not raw_text:
            print("所有方法都失败，回退到通用解析器")
            return parse_generic_balance(html_content, xpath)
        
        print(f"最终提取的余额文本: {raw_text}")
        
        # 解析余额信息
        balance_info = parse_sophnet_balance_text(raw_text)
        
        return {
            'balance': balance_info,
            'raw_text': raw_text
        }
        
    except Exception as e:
        print(f"Sophnet解析器错误: {str(e)}")
        import traceback
        traceback.print_exc()
        # 回退到通用解析器
        return parse_generic_balance(html_content, xpath)

def parse_sophnet_balance_text(text):
    """解析Sophnet余额文本"""
    print(f"解析Sophnet余额文本: {text}")
    
    # 提取总余额 - 查找 ￥数字 格式
    total_match = re.search(r'￥(\d+(?:\.\d+)?)', text)
    total = f"￥{total_match.group(1)}" if total_match else "￥0"
    
    # 提取充值余额 - 查找 充值余额：￥数字 格式
    recharge_match = re.search(r'充值余额：[^￥]*￥(\d+(?:\.\d+)?)', text)
    recharge = f"￥{recharge_match.group(1)}" if recharge_match else "￥0"
    
    # 提取赠送余额 - 查找 赠送余额：￥数字 格式
    gift_match = re.search(r'赠送余额：[^￥]*￥(\d+(?:\.\d+)?)', text)
    gift = f"￥{gift_match.group(1)}" if gift_match else "￥0"
    
    result = {
        'total': total,
        'recharge': recharge,
        'gift': gift
    }
    
    print(f"解析结果: {result}")
    return result

def parse_generic_balance(html_content, xpath):
    """通用余额解析器 - 使用BeautifulSoup和CSS选择器"""
    try:
        print(f"使用通用解析器，XPath: {xpath}")
        
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 尝试多种方法查找余额信息
        element_text = ""
        
        # 方法1: 查找包含"余额"文本的元素
        balance_elements = soup.find_all(string=re.compile(r'余额'))
        if balance_elements:
            for elem in balance_elements:
                parent = elem.parent
                if parent:
                    text = parent.get_text(strip=True)
                    if '￥' in text:
                        element_text = text
                        print(f"通过文本搜索找到余额: {element_text}")
                        break
        
        # 方法2: 查找包含货币符号的元素
        if not element_text:
            money_elements = soup.find_all(string=re.compile(r'￥\d+'))
            if money_elements:
                for elem in money_elements:
                    parent = elem.parent
                    if parent:
                        text = parent.get_text(strip=True)
                        if '余额' in text or '充值' in text or '赠送' in text:
                            element_text = text
                            print(f"通过货币符号找到余额: {element_text}")
                            break
        
        # 方法3: 查找可能的余额容器
        if not element_text:
            possible_containers = soup.find_all('div', class_=re.compile(r'balance|money|amount'))
            for container in possible_containers:
                text = container.get_text(strip=True)
                if '￥' in text:
                    element_text = text
                    print(f"通过容器类名找到余额: {element_text}")
                    break
        
        if not element_text:
            # 最后尝试：搜索整个页面中包含余额信息的文本
            all_text = soup.get_text()
            balance_match = re.search(r'余额[：:][^￥]*￥\d+[^）]*（[^）]*）', all_text)
            if balance_match:
                element_text = balance_match.group(0)
                print(f"通过全文搜索找到余额: {element_text}")
        
        if not element_text:
            raise ValueError("未找到余额信息")
        
        # 解析余额信息
        balance_info = parse_balance_text(element_text)
        
        return {
            'balance': balance_info,
            'raw_text': element_text
        }
        
    except Exception as e:
        print(f"通用解析器错误: {str(e)}")
        raise

def parse_balance_text(text):
    """通用余额文本解析"""
    print(f"解析余额文本: {text}")
    
    # 匹配总余额
    total_match = re.search(r'￥(\d+(?:\.\d+)?)', text)
    total = f"￥{total_match.group(1)}" if total_match else "￥0"
    
    # 匹配充值余额
    recharge_match = re.search(r'充值余额：[^￥]*￥(\d+(?:\.\d+)?)', text)
    recharge = f"￥{recharge_match.group(1)}" if recharge_match else "￥0"
    
    # 匹配赠送余额
    gift_match = re.search(r'赠送余额：[^￥]*￥(\d+(?:\.\d+)?)', text)
    gift = f"￥{gift_match.group(1)}" if gift_match else "￥0"
    
    result = {
        'total': total,
        'recharge': recharge,
        'gift': gift
    }
    
    print(f"通用解析结果: {result}")
    return result

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'ok', 'message': '代理服务运行正常'})

if __name__ == '__main__':
    print("启动余额查询代理服务...")
    print("服务地址: http://localhost:5000")
    print("健康检查: http://localhost:5000/health")
    app.run(host='0.0.0.0', port=5000, debug=True)