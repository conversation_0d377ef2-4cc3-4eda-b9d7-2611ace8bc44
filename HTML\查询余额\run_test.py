#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动服务器并运行测试
"""

import subprocess
import time
import requests
import json
import threading
import sys

def start_server():
    """启动代理服务器"""
    try:
        print("启动代理服务器...")
        process = subprocess.Popen([sys.executable, 'proxy-server.py'], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True)
        return process
    except Exception as e:
        print(f"启动服务器失败: {e}")
        return None

def wait_for_server(max_wait=10):
    """等待服务器启动"""
    for i in range(max_wait):
        try:
            response = requests.get('http://localhost:5000/health', timeout=2)
            if response.status_code == 200:
                print("服务器启动成功!")
                return True
        except:
            pass
        print(f"等待服务器启动... ({i+1}/{max_wait})")
        time.sleep(1)
    return False

def test_query():
    """测试查询功能"""
    test_data = {
        "url": "https://www.sophnet.com/#/organization/overview",
        "cookies": "_c_WBKFRo=EwtDcWAquCLRVdmVQc7X77z6SEcTiwODDQnNuKbo; _nb_ioWEgULi=; authorized-token={%22accessToken%22:%22eyJhbGciOiJIUzI1NiJ9.eyJydGkiOjM4MDUxLCJuYW1lIjoi5Lq65bel5pm66IO95a6I5oqk6ICFMzY5OCIsInRva2VuVHlwZSI6ImFjY2Vzc190b2tlbiIsInVzZXJJZCI6MjUyNjUsImV4cCI6MTc1MjY2MjAxMX0._10ow0abvB5An0yT4c7XujQERoUkVoXEwr_N-dVb5yQ%22%2C%22expires%22:1752662011177%2C%22refreshToken%22:%22ByZLoPWvXtw4FHvaRNYRejrBgkcSmtwyTOJUh-ZEKqleh4EhR_tObEJQK2x1lDvQkP6KaEio0xNXPmNKujxJcg%22}; multiple-tabs=true; JSESSIONID=B32B8C447AADE83E2780B5A8204C8BE9",
        "xpath": "/html/body/div[1]/div[1]/div[2]/div/div[2]/section/div/div/div[1]/div/div/div/div[1]/div[2]",
        "siteName": "sophnet"
    }
    
    try:
        print("\n开始测试查询功能...")
        print(f"目标URL: {test_data['url']}")
        
        response = requests.post(
            'http://localhost:5000/query',
            headers={'Content-Type': 'application/json'},
            json=test_data,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("查询成功!")
            print(f"使用解析器: {result.get('parser_used', 'unknown')}")
            print(f"余额信息:")
            for key, value in result['balance'].items():
                print(f"  {key}: {value}")
            if result.get('raw_text'):
                print(f"原始文本: {result['raw_text']}")
            return True
        else:
            error_data = response.json() if 'application/json' in response.headers.get('content-type', '') else {}
            print(f"查询失败: {error_data.get('error', response.text)}")
            return False
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return False

def main():
    print("=" * 60)
    print("余额查询系统集成测试")
    print("=" * 60)
    
    # 启动服务器
    server_process = start_server()
    if not server_process:
        print("无法启动服务器，退出测试")
        return
    
    try:
        # 等待服务器启动
        if not wait_for_server():
            print("服务器启动超时")
            return
        
        # 运行测试
        success = test_query()
        
        print("\n" + "=" * 60)
        print(f"测试结果: {'成功' if success else '失败'}")
        print("=" * 60)
        
        if success:
            print("\n✅ 系统运行正常！")
            print("现在可以打开 test.html 使用网页界面进行查询")
        else:
            print("\n❌ 系统存在问题，请检查日志")
            
    finally:
        # 清理
        if server_process:
            print("\n关闭服务器...")
            server_process.terminate()
            server_process.wait()

if __name__ == '__main__':
    main()