<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>余额查询工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            width: 90%;
            max-width: 500px;
        }
        
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: 600;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        input, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        input:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .balance-info {
            font-size: 18px;
            color: #333;
        }
        
        .balance-amount {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin: 10px 0;
        }
        
        .balance-detail {
            font-size: 14px;
            color: #666;
            margin-top: 10px;
        }
        
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            border-left-color: #e74c3c;
        }
        
        .loading {
            text-align: center;
            color: #667eea;
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        /* 新增样式 */
        select.form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }
        
        select.form-control:focus {
            outline: none;
            border-color: #667eea;
        }
        
        select.form-control:disabled {
            background: #f8f9fa;
            cursor: not-allowed;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            flex: 1;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            flex: 1;
        }
        
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e1e5e9;
        }
        
        .panel-header h2 {
            color: #333;
            font-size: 20px;
            margin: 0;
        }
        
        .btn-close {
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .tab-btn {
            background: none;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab-btn.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .account-item {
            background: #f8f9fa;
            border: 1px solid #e1e5e9;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
        }
        
        .account-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .account-title {
            font-weight: 600;
            color: #333;
        }
        
        .account-actions {
            display: flex;
            gap: 5px;
        }
        
        .btn-small {
            padding: 5px 10px;
            font-size: 12px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .btn-edit {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .account-info {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">余额查询工具</h1>
        
        <!-- 主查询界面 -->
        <div id="mainPanel">
            <div class="form-group">
                <label for="siteSelect">选择网站：</label>
                <select id="siteSelect" class="form-control">
                    <option value="">请选择网站</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="accountSelect">选择账号：</label>
                <select id="accountSelect" class="form-control" disabled>
                    <option value="">请先选择网站</option>
                </select>
            </div>
            
            <div class="button-group">
                <button type="button" class="btn btn-primary" id="queryBtn" disabled>查询余额</button>
                <button type="button" class="btn btn-secondary" id="settingsBtn">管理设置</button>
            </div>
        </div>
        
        <!-- 设置面板 -->
        <div id="settingsPanel" style="display: none;">
            <div class="panel-header">
                <h2>网站账号管理</h2>
                <button type="button" class="btn-close" id="closeSettings">×</button>
            </div>
            
            <div class="tabs">
                <button type="button" class="tab-btn active" data-tab="add">添加账号</button>
                <button type="button" class="tab-btn" data-tab="manage">管理账号</button>
            </div>
            
            <!-- 添加账号标签页 -->
            <div id="addTab" class="tab-content active">
                <form id="addForm">
                    <div class="form-group">
                        <label for="siteName">网站名称：</label>
                        <input type="text" id="siteName" placeholder="例如：Sophnet" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="accountName">账号名称：</label>
                        <input type="text" id="accountName" placeholder="例如：主账号" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="url">查询网址：</label>
                        <input type="url" id="url" placeholder="https://example.com" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="cookies">Cookies：</label>
                        <textarea id="cookies" placeholder="请输入完整的cookies信息" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="xpath">XPath路径：</label>
                        <input type="text" id="xpath" placeholder="/html/body/div[1]/..." required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">保存账号</button>
                </form>
            </div>
            
            <!-- 管理账号标签页 -->
            <div id="manageTab" class="tab-content">
                <div id="accountsList"></div>
            </div>
        </div>
        
        <div id="result" class="result" style="display: none;">
            <div id="resultContent"></div>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        class BalanceQueryManager {
            constructor() {
                this.accounts = this.loadAccounts();
                this.currentEditingId = null;
                
                this.initElements();
                this.initEventListeners();
                this.loadSiteOptions();
                
                // 预设默认账号
                this.initDefaultAccount();
            }
            
            initElements() {
                this.siteSelect = document.getElementById('siteSelect');
                this.accountSelect = document.getElementById('accountSelect');
                this.queryBtn = document.getElementById('queryBtn');
                this.settingsBtn = document.getElementById('settingsBtn');
                this.mainPanel = document.getElementById('mainPanel');
                this.settingsPanel = document.getElementById('settingsPanel');
                this.closeSettings = document.getElementById('closeSettings');
                this.addForm = document.getElementById('addForm');
                this.accountsList = document.getElementById('accountsList');
                this.resultDiv = document.getElementById('result');
                this.resultContent = document.getElementById('resultContent');
                this.statusDiv = document.getElementById('status');
            }
            
            initEventListeners() {
                // 主界面事件
                this.siteSelect.addEventListener('change', () => this.onSiteChange());
                this.accountSelect.addEventListener('change', () => this.onAccountChange());
                this.queryBtn.addEventListener('click', () => this.queryBalance());
                this.settingsBtn.addEventListener('click', () => this.showSettings());
                this.closeSettings.addEventListener('click', () => this.hideSettings());
                
                // 设置面板事件
                this.addForm.addEventListener('submit', (e) => this.saveAccount(e));
                
                // 标签页切换
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
                });
            }
            
            initDefaultAccount() {
                if (this.accounts.length === 0) {
                    const defaultAccount = {
                        id: Date.now(),
                        siteName: 'Sophnet',
                        accountName: '主账号',
                        url: 'https://www.sophnet.com/#/organization/overview',
                        cookies: '_c_WBKFRo=EwtDcWAquCLRVdmVQc7X77z6SEcTiwODDQnNuKbo; _nb_ioWEgULi=; authorized-token={%22accessToken%22:%22eyJhbGciOiJIUzI1NiJ9.eyJydGkiOjM4MDUxLCJuYW1lIjoi5Lq65bel5pm66IO95a6I5oqk6ICFMzY5OCIsInRva2VuVHlwZSI6ImFjY2Vzc190b2tlbiIsInVzZXJJZCI6MjUyNjUsImV4cCI6MTc1MjY2MjAxMX0._10ow0abvB5An0yT4c7XujQERoUkVoXEwr_N-dVb5yQ%22%2C%22expires%22:*************%2C%22refreshToken%22:%22ByZLoPWvXtw4FHvaRNYRejrBgkcSmtwyTOJUh-ZEKqleh4EhR_tObEJQK2x1lDvQkP6KaEio0xNXPmNKujxJcg%22}; multiple-tabs=true; JSESSIONID=B32B8C447AADE83E2780B5A8204C8BE9',
                        xpath: '/html/body/div[1]/div[1]/div[2]/div/div[2]/section/div/div/div[1]/div/div/div/div[1]/div[2]'
                    };
                    this.accounts.push(defaultAccount);
                    this.saveAccounts();
                }
            }
            
            loadAccounts() {
                const saved = localStorage.getItem('balanceQueryAccounts');
                return saved ? JSON.parse(saved) : [];
            }
            
            saveAccounts() {
                localStorage.setItem('balanceQueryAccounts', JSON.stringify(this.accounts));
            }
            
            loadSiteOptions() {
                const sites = [...new Set(this.accounts.map(acc => acc.siteName))];
                this.siteSelect.innerHTML = '<option value="">请选择网站</option>';
                sites.forEach(site => {
                    const option = document.createElement('option');
                    option.value = site;
                    option.textContent = site;
                    this.siteSelect.appendChild(option);
                });
            }
            
            onSiteChange() {
                const selectedSite = this.siteSelect.value;
                this.accountSelect.innerHTML = '<option value="">请选择账号</option>';
                
                if (selectedSite) {
                    const siteAccounts = this.accounts.filter(acc => acc.siteName === selectedSite);
                    siteAccounts.forEach(acc => {
                        const option = document.createElement('option');
                        option.value = acc.id;
                        option.textContent = acc.accountName;
                        this.accountSelect.appendChild(option);
                    });
                    this.accountSelect.disabled = false;
                } else {
                    this.accountSelect.disabled = true;
                    this.queryBtn.disabled = true;
                }
            }
            
            onAccountChange() {
                this.queryBtn.disabled = !this.accountSelect.value;
            }
            
            async queryBalance() {
                const accountId = parseInt(this.accountSelect.value);
                const account = this.accounts.find(acc => acc.id === accountId);
                
                if (!account) {
                    this.showStatus('未找到账号信息', 'error');
                    return;
                }
                
                this.setLoading(true);
                this.hideResult();
                
                try {
                    // 首先尝试真实查询
                    const result = await this.realQuery(account);
                    this.showResult(result.balance, account, result.raw_text);
                    this.showStatus('查询成功！', 'success');
                } catch (error) {
                    // 如果真实查询失败，显示错误信息
                    console.error('查询失败:', error);
                    this.showError(`查询失败: ${error.message}`);
                } finally {
                    this.setLoading(false);
                }
            }
            
            async realQuery(account) {
                const proxyUrl = 'http://localhost:5000/query';
                
                const requestData = {
                    url: account.url,
                    cookies: account.cookies,
                    xpath: account.xpath,
                    siteName: account.siteName
                };
                
                console.log('发送查询请求:', requestData);
                
                const response = await fetch(proxyUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('查询响应:', data);
                
                if (!data.success) {
                    throw new Error(data.error || '查询失败');
                }
                
                return data;
            }
            
            showResult(balance, account, rawText = '') {
                this.resultContent.innerHTML = `
                    <div class="balance-info">${account.siteName} - ${account.accountName}</div>
                    <div class="balance-amount">${balance.total}</div>
                    <div class="balance-detail">
                        充值余额：<strong>${balance.recharge}</strong> + 
                        赠送余额：<strong>${balance.gift}</strong>
                    </div>
                    ${rawText ? `<div style="margin-top: 15px; padding: 10px; background: #f0f0f0; border-radius: 5px; font-size: 12px; color: #666;">原始数据: ${rawText}</div>` : ''}
                `;
                this.resultDiv.style.display = 'block';
                this.resultDiv.classList.remove('error');
            }
            
            showError(message) {
                this.resultContent.innerHTML = `
                    <div class="balance-info">查询失败</div>
                    <div style="color: #e74c3c; margin-top: 10px;">${message}</div>
                `;
                this.resultDiv.style.display = 'block';
                this.resultDiv.classList.add('error');
            }
            
            hideResult() {
                this.resultDiv.style.display = 'none';
            }
            
            setLoading(loading) {
                if (loading) {
                    this.queryBtn.disabled = true;
                    this.queryBtn.textContent = '查询中...';
                } else {
                    this.queryBtn.disabled = !this.accountSelect.value;
                    this.queryBtn.textContent = '查询余额';
                }
            }
            
            showSettings() {
                this.mainPanel.style.display = 'none';
                this.settingsPanel.style.display = 'block';
                this.loadAccountsList();
            }
            
            hideSettings() {
                this.settingsPanel.style.display = 'none';
                this.mainPanel.style.display = 'block';
                this.loadSiteOptions();
                this.resetForm();
            }
            
            switchTab(tabName) {
                // 切换标签按钮状态
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.tab === tabName);
                });
                
                // 切换标签内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.toggle('active', content.id === tabName + 'Tab');
                });
                
                if (tabName === 'manage') {
                    this.loadAccountsList();
                }
            }
            
            saveAccount(e) {
                e.preventDefault();
                
                const formData = new FormData(this.addForm);
                const account = {
                    id: this.currentEditingId || Date.now(),
                    siteName: document.getElementById('siteName').value,
                    accountName: document.getElementById('accountName').value,
                    url: document.getElementById('url').value,
                    cookies: document.getElementById('cookies').value,
                    xpath: document.getElementById('xpath').value
                };
                
                if (this.currentEditingId) {
                    // 编辑现有账号
                    const index = this.accounts.findIndex(acc => acc.id === this.currentEditingId);
                    this.accounts[index] = account;
                    this.currentEditingId = null;
                } else {
                    // 添加新账号
                    this.accounts.push(account);
                }
                
                this.saveAccounts();
                this.resetForm();
                this.showStatus('账号保存成功！', 'success');
                this.loadAccountsList();
            }
            
            loadAccountsList() {
                if (this.accounts.length === 0) {
                    this.accountsList.innerHTML = '<p style="text-align: center; color: #666;">暂无保存的账号</p>';
                    return;
                }
                
                this.accountsList.innerHTML = this.accounts.map(account => `
                    <div class="account-item">
                        <div class="account-header">
                            <div class="account-title">${account.siteName} - ${account.accountName}</div>
                            <div class="account-actions">
                                <button class="btn-small btn-edit" onclick="app.editAccount(${account.id})">编辑</button>
                                <button class="btn-small btn-delete" onclick="app.deleteAccount(${account.id})">删除</button>
                            </div>
                        </div>
                        <div class="account-info">
                            <div>网址: ${account.url}</div>
                            <div>XPath: ${account.xpath}</div>
                        </div>
                    </div>
                `).join('');
            }
            
            editAccount(id) {
                const account = this.accounts.find(acc => acc.id === id);
                if (!account) return;
                
                this.currentEditingId = id;
                document.getElementById('siteName').value = account.siteName;
                document.getElementById('accountName').value = account.accountName;
                document.getElementById('url').value = account.url;
                document.getElementById('cookies').value = account.cookies;
                document.getElementById('xpath').value = account.xpath;
                
                this.switchTab('add');
            }
            
            deleteAccount(id) {
                if (confirm('确定要删除这个账号吗？')) {
                    this.accounts = this.accounts.filter(acc => acc.id !== id);
                    this.saveAccounts();
                    this.loadAccountsList();
                    this.showStatus('账号删除成功！', 'success');
                }
            }
            
            resetForm() {
                this.addForm.reset();
                this.currentEditingId = null;
            }
            
            showStatus(message, type) {
                this.statusDiv.textContent = message;
                this.statusDiv.className = `status ${type}`;
                this.statusDiv.style.display = 'block';
                
                setTimeout(() => {
                    this.statusDiv.style.display = 'none';
                }, 3000);
            }
        }
        
        // 全局变量，供HTML中的onclick使用
        let app;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            app = new BalanceQueryManager();
        });
    </script>
</body>
</html>