#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试解析功能
"""

from bs4 import BeautifulSoup
import re

def test_sophnet_parsing():
    """测试Sophnet余额解析"""
    
    # 模拟的HTML内容
    html_content = '''
    <html>
    <body>
        <div class="flex items-center mt-3">
            <div class="text-base text-[#4E5969]">余额：</div>
            <div class="text-[24px] font-bold mr-4">￥20</div>
            <div class="text-sm text-[#4E5969]"> （充值余额：<span class="font-bold">￥0</span> + 赠送余额：<span class="font-bold">￥20</span>） </div>
        </div>
    </body>
    </html>
    '''
    
    print("测试Sophnet余额解析...")
    print("HTML内容:")
    print(html_content)
    
    # 使用BeautifulSoup解析
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 查找余额容器
    balance_div = soup.find('div', class_='flex items-center mt-3')
    
    if balance_div:
        raw_text = balance_div.get_text(strip=True)
        print(f"\n找到余额div，内容: {raw_text}")
        
        # 解析余额信息
        balance_info = parse_sophnet_balance_text(raw_text)
        print(f"解析结果: {balance_info}")
        
        return True
    else:
        print("未找到余额div")
        return False

def parse_sophnet_balance_text(text):
    """解析Sophnet余额文本"""
    print(f"解析余额文本: {text}")
    
    # 提取总余额 - 查找 ￥数字 格式
    total_match = re.search(r'￥(\d+(?:\.\d+)?)', text)
    total = f"￥{total_match.group(1)}" if total_match else "￥0"
    
    # 提取充值余额 - 查找 充值余额：￥数字 格式
    recharge_match = re.search(r'充值余额：[^￥]*￥(\d+(?:\.\d+)?)', text)
    recharge = f"￥{recharge_match.group(1)}" if recharge_match else "￥0"
    
    # 提取赠送余额 - 查找 赠送余额：￥数字 格式
    gift_match = re.search(r'赠送余额：[^￥]*￥(\d+(?:\.\d+)?)', text)
    gift = f"￥{gift_match.group(1)}" if gift_match else "￥0"
    
    result = {
        'total': total,
        'recharge': recharge,
        'gift': gift
    }
    
    return result

def test_generic_parsing():
    """测试通用解析"""
    
    # 模拟的HTML内容
    html_content = '''
    <html>
    <body>
        <div class="balance-container">
            <p>账户余额：￥100</p>
            <p>充值余额：￥50 + 赠送余额：￥50</p>
        </div>
    </body>
    </html>
    '''
    
    print("\n测试通用余额解析...")
    print("HTML内容:")
    print(html_content)
    
    # 使用BeautifulSoup解析
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 查找包含"余额"文本的元素
    balance_elements = soup.find_all(string=re.compile(r'余额'))
    
    element_text = ""
    if balance_elements:
        for elem in balance_elements:
            parent = elem.parent
            if parent:
                text = parent.get_text(strip=True)
                if '￥' in text:
                    element_text += text + " "
    
    if element_text:
        print(f"找到余额信息: {element_text.strip()}")
        balance_info = parse_balance_text(element_text.strip())
        print(f"解析结果: {balance_info}")
        return True
    else:
        print("未找到余额信息")
        return False

def parse_balance_text(text):
    """通用余额文本解析"""
    print(f"解析余额文本: {text}")
    
    # 匹配总余额
    total_match = re.search(r'账户余额：￥(\d+(?:\.\d+)?)', text)
    if not total_match:
        total_match = re.search(r'￥(\d+(?:\.\d+)?)', text)
    total = f"￥{total_match.group(1)}" if total_match else "￥0"
    
    # 匹配充值余额
    recharge_match = re.search(r'充值余额：￥(\d+(?:\.\d+)?)', text)
    recharge = f"￥{recharge_match.group(1)}" if recharge_match else "￥0"
    
    # 匹配赠送余额
    gift_match = re.search(r'赠送余额：￥(\d+(?:\.\d+)?)', text)
    gift = f"￥{gift_match.group(1)}" if gift_match else "￥0"
    
    result = {
        'total': total,
        'recharge': recharge,
        'gift': gift
    }
    
    return result

if __name__ == '__main__':
    print("=" * 50)
    print("余额解析功能测试")
    print("=" * 50)
    
    # 测试Sophnet解析
    sophnet_success = test_sophnet_parsing()
    
    # 测试通用解析
    generic_success = test_generic_parsing()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"Sophnet解析: {'成功' if sophnet_success else '失败'}")
    print(f"通用解析: {'成功' if generic_success else '失败'}")
    print("=" * 50)