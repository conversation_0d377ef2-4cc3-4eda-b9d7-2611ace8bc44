#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代理服务器功能
"""

import requests
import json

def test_server():
    """测试代理服务器"""
    
    # 测试数据
    test_data = {
        "url": "https://www.sophnet.com/#/organization/overview",
        "cookies": "_c_WBKFRo=EwtDcWAquCLRVdmVQc7X77z6SEcTiwODDQnNuKbo; _nb_ioWEgULi=; authorized-token={%22accessToken%22:%22eyJhbGciOiJIUzI1NiJ9.eyJydGkiOjM4MDUxLCJuYW1lIjoi5Lq65bel5pm66IO95a6I5oqk6ICFMzY5OCIsInRva2VuVHlwZSI6ImFjY2Vzc190b2tlbiIsInVzZXJJZCI6MjUyNjUsImV4cCI6MTc1MjY2MjAxMX0._10ow0abvB5An0yT4c7XujQERoUkVoXEwr_N-dVb5yQ%22%2C%22expires%22:1752662011177%2C%22refreshToken%22:%22ByZLoPWvXtw4FHvaRNYRejrBgkcSmtwyTOJUh-ZEKqleh4EhR_tObEJQK2x1lDvQkP6KaEio0xNXPmNKujxJcg%22}; multiple-tabs=true; JSESSIONID=B32B8C447AADE83E2780B5A8204C8BE9",
        "xpath": "/html/body/div[1]/div[1]/div[2]/div/div[2]/section/div/div/div[1]/div/div/div/div[1]/div[2]",
        "siteName": "Sophnet"
    }
    
    try:
        print("测试代理服务器...")
        print(f"请求URL: {test_data['url']}")
        print(f"网站名称: {test_data['siteName']}")
        
        # 发送POST请求
        response = requests.post(
            'http://localhost:5000/query',
            headers={'Content-Type': 'application/json'},
            json=test_data,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("查询成功!")
            print(f"解析器: {result.get('parser_used', 'unknown')}")
            print(f"余额信息: {json.dumps(result['balance'], ensure_ascii=False, indent=2)}")
            print(f"原始文本: {result.get('raw_text', 'N/A')}")
        else:
            error_data = response.json() if response.headers.get('content-type') == 'application/json' else {}
            print(f"查询失败: {error_data.get('error', response.text)}")
            
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到代理服务器")
        print("请确保代理服务器已启动 (python proxy-server.py)")
    except Exception as e:
        print(f"测试失败: {str(e)}")

def test_health():
    """测试服务器健康状态"""
    try:
        response = requests.get('http://localhost:5000/health', timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"服务器状态: {result['status']}")
            print(f"消息: {result['message']}")
            return True
        else:
            print(f"健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"健康检查错误: {str(e)}")
        return False

if __name__ == '__main__':
    print("=" * 50)
    print("余额查询代理服务器测试")
    print("=" * 50)
    
    # 先测试健康状态
    print("\n1. 测试服务器健康状态...")
    if test_health():
        print("\n2. 测试余额查询功能...")
        test_server()
    else:
        print("\n服务器未启动或不可用")
        print("请先运行: python proxy-server.py")
    
    print("\n测试完成!")