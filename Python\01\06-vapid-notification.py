import json
import logging
from flask import Flask, render_template, request, send_from_directory
from pywebpush import webpush, WebPushException

app = Flask(__name__)

# 存储订阅信息（在生产环境中，应使用数据库）
subscriptions = []

# --- 请替换成您自己的 VAPID 密钥 ---
VAPID_PRIVATE_KEY = "EK9axTors0IckpeVkfootmLREnoZ6lED-6o1bUBGlDY"
VAPID_PUBLIC_KEY = "BDrMqpFChD5fazO-_RnWcEv8nejBFBO56XioTsHHF063LnEunCkktvQTvrhZxAoc2RhvmUGkS8mB-a2hcPl_O0w"

@app.route('/')
def index():
    return render_template('index.html', vapid_public_key=VAPID_PUBLIC_KEY)

@app.route('/static/<path:path>')
def send_static(path):
    return send_from_directory('static', path)

@app.route('/subscription', methods=['POST'])
def subscription():
    """接收并存储前端发送的订阅信息"""
    sub_info = request.get_json()
    if not sub_info:
        return "No subscription information", 400

    print(f"New subscription received: {sub_info}")
    subscriptions.append(sub_info)
    return "Subscription saved.", 201

@app.route('/send_notification', methods=['POST'])
def send_notification():
    """向所有订阅者发送通知"""
    if not subscriptions:
        return "No subscribers to notify.", 404

    message_data = request.get_json()
    if not message_data or 'title' not in message_data or 'body' not in message_data:
        return "Invalid message format. Required: {'title': '...', 'body': '...'}", 400

    print(f"Sending notification: {message_data}")

    for sub_info in subscriptions:
        try:
            webpush(
                subscription_info=sub_info,
                data=json.dumps(message_data),
                vapid_private_key=VAPID_PRIVATE_KEY,
                vapid_claims={
                    "sub": "mailto:<EMAIL>" # 请替换成您的邮箱
                }
            )
        except WebPushException as ex:
            print(f"Error sending push to {sub_info.get('endpoint', '')}: {ex}")
            # 在实际应用中，如果收到 404 或 410 错误，应从数据库中删除该订阅

    return f"Notification sent to {len(subscriptions)} subscriber(s).", 200

if __name__ == '__main__':
    # 在运行前，请确保您已经生成并替换了 VAPID 密钥
    if VAPID_PRIVATE_KEY == "YOUR_PRIVATE_KEY" or VAPID_PUBLIC_KEY == "YOUR_PUBLIC_KEY":
        print("\n*** [警告] ***")
        print("请先生成 VAPID 密钥对，并替换 '06-vapid-notification.py' 文件中的占位符。")
        print("使用 'vapid --gen' 命令生成密钥。")
        print("************\n")
    app.run(debug=True, port=5001)
