import base64, secrets
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.primitives import serialization

def b64url(data: bytes) -> str:
    return base64.urlsafe_b64encode(data).rstrip(b'=').decode()

# 生成 P-256 私钥
priv_key = ec.generate_private_key(ec.SECP256R1())

# raw 32-byte 私钥
d_bytes = priv_key.private_numbers().private_value.to_bytes(32, 'big')
private_key = b64url(d_bytes)

# 未压缩公钥 (0x04 | X | Y)
pub_numbers = priv_key.public_key().public_numbers()
x_bytes = pub_numbers.x.to_bytes(32, 'big')
y_bytes = pub_numbers.y.to_bytes(32, 'big')
public_key = b64url(b'\x04' + x_bytes + y_bytes)

print('Private Key:', private_key)
print('Public  Key:', public_key)