console.log('Service Worker 已加载');

self.addEventListener('push', function(event) {
    console.log('[Service Worker] 收到推送事件');

    const data = event.data.json();
    console.log('推送内容:', data);

    const title = data.title || '收到新消息';
    const options = {
        body: data.body || '您有一条新通知。',
        icon: 'images/icon.png', // 可选：通知图标
        badge: 'images/badge.png' // 可选：小图标
    };

    event.waitUntil(self.registration.showNotification(title, options));
});
