<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Web Push Notification Demo</title>
    <style>
        body { font-family: sans-serif; padding: 20px; }
        button { padding: 10px 15px; font-size: 16px; cursor: pointer; }
        #status { margin-top: 20px; font-style: italic; color: #555; }
    </style>
</head>
<body>

    <h1>VAPID Web Push Notification</h1>
    <p>点击按钮以订阅或发送通知。</p>

    <button id="subscribeBtn">订阅通知</button>
    <button id="sendNotificationBtn">发送测试通知</button>

    <div id="status"></div>

    <script>
        const VAPID_PUBLIC_KEY = '{{ vapid_public_key }}';
        const subscribeBtn = document.getElementById('subscribeBtn');
        const sendNotificationBtn = document.getElementById('sendNotificationBtn');
        const statusDiv = document.getElementById('status');

        // 将 base64 字符串转换为 Uint8Array
        function urlBase64ToUint8Array(base64String) {
            const padding = '='.repeat((4 - base64String.length % 4) % 4);
            const base64 = (base64String + padding).replace(/\-/g, '+').replace(/_/g, '/');
            const rawData = window.atob(base64);
            const outputArray = new Uint8Array(rawData.length);
            for (let i = 0; i < rawData.length; ++i) {
                outputArray[i] = rawData.charCodeAt(i);
            }
            return outputArray;
        }

        async function registerAndSubscribe() {
            if ('serviceWorker' in navigator && 'PushManager' in window) {
                statusDiv.textContent = '正在注册 Service Worker...';
                try {
                    const registration = await navigator.serviceWorker.register('/static/service-worker.js');
                    statusDiv.textContent = 'Service Worker 已注册。';

                    statusDiv.textContent = '正在请求通知权限...';
                    const permission = await window.Notification.requestPermission();
                    if (permission !== 'granted') {
                        throw new Error('通知权限被拒绝。');
                    }
                    statusDiv.textContent = '通知权限已授予。';

                    statusDiv.textContent = '正在获取推送订阅...';
                    const subscription = await registration.pushManager.subscribe({
                        userVisibleOnly: true,
                        applicationServerKey: urlBase64ToUint8Array(VAPID_PUBLIC_KEY)
                    });
                    statusDiv.textContent = '已成功获取推送订阅。';

                    // 将订阅信息发送到后端
                    await fetch('/subscription', {
                        method: 'POST',
                        body: JSON.stringify(subscription),
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    statusDiv.textContent = '订阅信息已成功发送到服务器！';
                    subscribeBtn.disabled = true;

                } catch (error) {
                    console.error('订阅失败:', error);
                    statusDiv.textContent = `操作失败: ${error.message}`;
                }
            } else {
                statusDiv.textContent = '此浏览器不支持推送通知。';
            }
        }

        async function sendNotification() {
            statusDiv.textContent = '正在请求发送通知...';
            try {
                const response = await fetch('/send_notification', {
                    method: 'POST',
                    body: JSON.stringify({ title: '你好！', body: '这是一条来自服务器的测试通知。' }),
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const text = await response.text();
                statusDiv.textContent = `服务器响应: ${text}`;
            } catch (error) {
                console.error('发送通知失败:', error);
                statusDiv.textContent = `发送失败: ${error.message}`;
            }
        }

        subscribeBtn.addEventListener('click', registerAndSubscribe);
        sendNotificationBtn.addEventListener('click', sendNotification);

    </script>

</body>
</html>
