import requests
import json

def get_expense_bill():
    """
    获取SiliconFlow费用账单信息
    """
    print("🔍 正在获取费用账单信息...")
    
    # 完全按照提供的HTTP请求头配置
    headers = {
        'Host': 'cloud.siliconflow.cn',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Referer': 'https://account.siliconflow.cn/',
        'Sec-GPC': '1',
        'Connection': 'keep-alive',
        'Cookie': '_ga_FS03N2E4YL=GS1.1.**********.4.1.**********.60.0.0; _ga=GA1.1.**********.**********; _ga_E3E286SJLR=GS1.1.**********.4.1.**********.60.0.0; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%22019619a4-c147-7bfa-8329-46eff2282691%22%2C%22%24sesid%22%3A%5B1753230785411%2C%22019834b2-a9f0-7cf2-b236-3cd3f7c7cc36%22%2C1753230780912%5D%7D; __SF_auth.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoieUtkdXFTY19KZ2h0RGQwb01hclFFVWtMdWxPcEJGcnN4MDRmTzZnbElhaEZjQUxHYUoxZWlZMDlZbHZPU0VMVmk0NVg1VnVIUFFWMW1tTnJWTnpmSVEifQ..rSG70J_BhUkG6m_daLTGwQ.HGbMjP8Oh4HdM55cvjHvdESkBPPIjXF4OMOzji_Rs3KCJ15GIsCK0r2k45ZoWnjQdk1e_xTIJad9tzcRBiatmS-Km4aQVRWbnUjWNocp118ZEExFiisi6QXXciS1net3rfPLWiKnGfweXrkk0FUDUbbPQWv_NWrWHqQV_LtoI_YR9EldGakXajdd34JsR9E3JQ_723EqEKp0Sh_gkq0T2LwyYeLcYQdmsNHqgGtnAm5lhukDAIdQYJmh2ovheHb_._zUNeS8T62ZptnkhzJ-c4WZDoCL6DeHJN610bGs_2A8; acw_tc=0a03334617532344042551677e53b32fa6a9e78581436046356a9b8724bc8b',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-site',
        'Sec-Fetch-User': '?1',
        'Priority': 'u=0, i'
    }
    
    # 目标URL
    url = 'https://cloud.siliconflow.cn/sft-clx69sdbu00017gtgxj884jwa/expensebill'
    
    try:
        print(f"🚀 请求URL: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 尝试解析JSON
            try:
                data = response.json()
                print("✅ 成功获取JSON数据:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                
                # 查找余额相关信息
                balance_keys = ['balance', 'amount', 'credit', 'quota', 'remaining', 'total', 'used', 'available']
                print("\n💰 查找余额信息:")
                
                def find_balance_info(obj, path=""):
                    if isinstance(obj, dict):
                        for key, value in obj.items():
                            current_path = f"{path}.{key}" if path else key
                            if key.lower() in [k.lower() for k in balance_keys]:
                                print(f"  - {current_path}: {value}")
                            if isinstance(value, (dict, list)):
                                find_balance_info(value, current_path)
                    elif isinstance(obj, list):
                        for i, item in enumerate(obj):
                            find_balance_info(item, f"{path}[{i}]")
                
                find_balance_info(data)
                return data
                
            except json.JSONDecodeError:
                print("⚠️ 响应不是JSON格式，显示HTML内容:")
                print(response.text[:1000])
                return response.text
                
        elif response.status_code == 401:
            print("🔐 需要认证 - 请检查Cookie和session token是否有效")
        elif response.status_code == 403:
            print("🚫 权限不足 - 可能需要更新认证信息")
        elif response.status_code == 404:
            print("❌ 页面未找到 - URL可能已过期或无效")
        else:
            print(f"⚠️ 其他状态码: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
    except requests.exceptions.ConnectionError:
        print("🔌 连接错误")
    except Exception as e:
        print(f"❌ 请求错误: {e}")
    
    return None

if __name__ == "__main__":
    print("🚀 开始探索SiliconFlow API端点并获取余额信息...")
    print("=" * 60)
    
    # 探索API端点
    successful_endpoints = discover_api_endpoints()
    
    print("\n" + "=" * 60)
    print("📋 探索结果总结:")
    
    if successful_endpoints:
        print(f"✅ 发现 {len(successful_endpoints)} 个可用端点:")
        for url, data in successful_endpoints:
            print(f"  - {url}")
        
        print("\n🎯 尝试从发现的端点获取余额信息...")
        
        # 准备API请求头
        api_headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Connection': 'keep-alive',
            'Cookie': '_ga_FS03N2E4YL=GS1.1.**********.4.1.**********.60.0.0; _ga=GA1.1.**********.**********; _ga_E3E286SJLR=GS1.1.**********.4.1.**********.60.0.0; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%22019619a4-c147-7bfa-8329-46eff2282691%22%2C%22%24sesid%22%3A%5B1753230785411%2C%22019834b2-a9f0-7cf2-b236-3cd3f7c7cc36%22%2C1753230780912%5D%7D; __SF_auth.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoieUtkdXFTY19KZ2h0RGQwb01hclFFVWtMdWxPcEJGcnN4MDRmTzZnbElhaEZjQUxHYUoxZWlZMDlZbHZPU0VMVmk0NVg1VnVIUFFWMW1tTnJWTnpmSVEifQ..rSG70J_BhUkG6m_daLTGwQ.HGbMjP8Oh4HdM55cvjHvdESkBPPIjXF4OMOzji_Rs3KCJ15GIsCK0r2k45ZoWnjQdk1e_xTIJad9tzcRBiatmS-Km4aQVRWbnUjWNocp118ZEExFiisi6QXXciS1net3rfPLWiKnGfweXrkk0FUDUbbPQWv_NWrWHqQV_LtoI_YR9EldGakXajdd34JsR9E3JQ_723EqEKp0Sh_gkq0T2LwyYeLcYQdmsNHqgGtnAm5lhukDAIdQYJmh2ovheHb_._zUNeS8T62ZptnkhzJ-c4WZDoCL6DeHJN610bGs_2A8; acw_tc=0a03334617532344042551677e53b32fa6a9e78581436046356a9b8724bc8b',
            'Referer': 'https://account.siliconflow.cn/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0'
        }
        
        # 尝试从每个成功的端点获取余额
        for url, _ in successful_endpoints:
            # 更新Host头
            host = url.replace('https://', '').replace('http://', '').split('/')[0]
            api_headers['Host'] = host
            
            result = get_balance_from_endpoint(url, api_headers)
            if result:
                break
    else:
        print("❌ 未发现任何可用的API端点")
        print("\n💡 建议:")
        print("1. 检查Cookie是否过期")
        print("2. 确认session token是否有效")
        print("3. 尝试在浏览器中手动访问账户页面")
        print("4. 查看浏览器开发者工具的网络请求")
    
    print("\n🏁 探索完成!")