#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级PDF文本替换编辑工具
基于PyMuPDF (fitz) 实现格式保持的PDF文本替换
参考GitHub最佳实践，保持原始布局和格式
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import warnings
from pathlib import Path
import tempfile
import os

# 抑制警告
warnings.filterwarnings("ignore")

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("PyMuPDF未安装，请运行: pip install PyMuPDF")

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False
    print("pdfplumber未安装，请运行: pip install pdfplumber")


class AdvancedPDFEditor:
    def __init__(self):
        self.current_pdf_path = None
        self.pdf_document = None
        self.original_text_blocks = []
        self.replacement_history = []
        
    def open_pdf(self, pdf_path):
        """打开PDF文档"""
        try:
            if PYMUPDF_AVAILABLE:
                self.pdf_document = fitz.open(pdf_path)
                self.current_pdf_path = pdf_path
                return True
            else:
                raise Exception("PyMuPDF未安装，无法使用高级功能")
        except Exception as e:
            raise Exception(f"PDF打开失败: {str(e)}")
    
    def extract_text_with_layout(self):
        """提取文本并保持布局信息"""
        if not self.pdf_document:
            raise Exception("请先打开PDF文件")
        
        try:
            text_blocks = []
            
            for page_num in range(len(self.pdf_document)):
                page = self.pdf_document[page_num]
                
                # 使用PyMuPDF的高级文本提取，保持布局
                blocks = page.get_text("dict")
                
                page_text = f"\n{'='*60}\n第 {page_num + 1} 页\n{'='*60}\n"
                
                for block in blocks["blocks"]:
                    if "lines" in block:  # 文本块
                        for line in block["lines"]:
                            line_text = ""
                            for span in line["spans"]:
                                line_text += span["text"]
                            if line_text.strip():
                                page_text += line_text + "\n"
                        page_text += "\n"  # 块之间的分隔
                
                text_blocks.append({
                    'page': page_num,
                    'text': page_text,
                    'blocks': blocks
                })
            
            self.original_text_blocks = text_blocks
            return "\n".join([block['text'] for block in text_blocks])
            
        except Exception as e:
            raise Exception(f"文本提取失败: {str(e)}")
    
    def replace_text_in_pdf(self, find_text, replace_text):
        """在PDF中直接替换文本，保持格式"""
        if not self.pdf_document:
            raise Exception("请先打开PDF文件")
        
        try:
            replacement_count = 0
            
            for page_num in range(len(self.pdf_document)):
                page = self.pdf_document[page_num]
                
                # 查找文本实例
                text_instances = page.search_for(find_text)
                
                for inst in text_instances:
                    # 获取文本区域的属性
                    text_dict = page.get_textbox(inst)
                    
                    if text_dict:
                        # 删除原文本
                        page.add_redact_annot(inst)
                        page.apply_redactions()
                        
                        # 在相同位置插入新文本
                        # 尝试保持原有的字体和大小
                        try:
                            page.insert_text(
                                inst.tl,  # 左上角位置
                                replace_text,
                                fontsize=12,  # 默认字体大小
                                color=(0, 0, 0)  # 黑色
                            )
                        except:
                            # 如果插入失败，使用简单方法
                            page.insert_text(inst.tl, replace_text)
                        
                        replacement_count += 1
            
            # 记录替换历史
            self.replacement_history.append({
                'find': find_text,
                'replace': replace_text,
                'count': replacement_count
            })
            
            return replacement_count
            
        except Exception as e:
            raise Exception(f"文本替换失败: {str(e)}")
    
    def save_pdf(self, output_path):
        """保存修改后的PDF"""
        if not self.pdf_document:
            raise Exception("没有可保存的PDF文档")
        
        try:
            self.pdf_document.save(output_path)
            return True
        except Exception as e:
            raise Exception(f"PDF保存失败: {str(e)}")
    
    def close_pdf(self):
        """关闭PDF文档"""
        if self.pdf_document:
            self.pdf_document.close()
            self.pdf_document = None
            self.current_pdf_path = None
            self.original_text_blocks = []


class AdvancedPDFEditorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("高级PDF文本替换编辑工具 - 保持原始格式")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)
        
        # 初始化PDF编辑器
        self.pdf_editor = AdvancedPDFEditor()
        self.current_text = ""
        
        # 检查依赖
        if not PYMUPDF_AVAILABLE:
            messagebox.showerror(
                "依赖缺失", 
                "PyMuPDF未安装，无法使用高级功能。\n请运行: pip install PyMuPDF"
            )
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面控件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题和说明
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        title_label = ttk.Label(
            title_frame, 
            text="🚀 高级PDF文本替换编辑工具", 
            font=('Arial', 18, 'bold')
        )
        title_label.pack()
        
        desc_label = ttk.Label(
            title_frame, 
            text="基于PyMuPDF技术，直接在PDF中替换文本，完美保持原始格式和布局",
            font=('Arial', 10),
            foreground="gray"
        )
        desc_label.pack(pady=(5, 0))
        
        # 文件操作区域
        file_frame = ttk.LabelFrame(main_frame, text="📁 文件操作", padding="10")
        file_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        file_frame.columnconfigure(1, weight=1)
        
        # 文件选择
        ttk.Button(file_frame, text="选择PDF文件", command=self.select_file).grid(row=0, column=0, padx=(0, 10))
        
        self.file_path_var = tk.StringVar(value="未选择文件")
        ttk.Label(file_frame, textvariable=self.file_path_var, foreground="gray").grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # 保存按钮
        self.save_button = ttk.Button(file_frame, text="保存PDF", command=self.save_pdf, state=tk.DISABLED)
        self.save_button.grid(row=0, column=2, padx=(10, 0))
        
        # 主要内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        content_frame.columnconfigure(0, weight=2)
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # 左侧：文本预览
        preview_frame = ttk.LabelFrame(content_frame, text="👀 PDF内容预览", padding="10")
        preview_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)
        
        self.preview_text = scrolledtext.ScrolledText(
            preview_frame,
            wrap=tk.WORD,
            width=70,
            height=35,
            font=('Consolas', 10),
            state=tk.DISABLED
        )
        self.preview_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 右侧：操作面板
        operation_frame = ttk.LabelFrame(content_frame, text="🔧 文本替换操作", padding="10")
        operation_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        operation_frame.columnconfigure(0, weight=1)
        
        # 查找文本
        ttk.Label(operation_frame, text="查找文本:", font=('Arial', 11, 'bold')).grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.find_text = scrolledtext.ScrolledText(operation_frame, width=40, height=6, font=('Arial', 10))
        self.find_text.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        # 替换文本
        ttk.Label(operation_frame, text="替换为:", font=('Arial', 11, 'bold')).grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.replace_text = scrolledtext.ScrolledText(operation_frame, width=40, height=6, font=('Arial', 10))
        self.replace_text.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # 操作按钮
        button_frame = ttk.Frame(operation_frame)
        button_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        button_frame.columnconfigure(0, weight=1)
        
        self.replace_button = ttk.Button(
            button_frame, 
            text="🔄 执行替换", 
            command=self.replace_text_action,
            state=tk.DISABLED
        )
        self.replace_button.grid(row=0, column=0, pady=(0, 10))
        
        # 替换历史
        history_label = ttk.Label(operation_frame, text="📝 替换历史:", font=('Arial', 11, 'bold'))
        history_label.grid(row=5, column=0, sticky=tk.W, pady=(0, 5))
        
        self.history_text = scrolledtext.ScrolledText(
            operation_frame, 
            width=40, 
            height=10, 
            font=('Consolas', 9),
            state=tk.DISABLED
        )
        self.history_text.grid(row=6, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        status_frame = ttk.Frame(main_frame, relief=tk.SUNKEN, borderwidth=1)
        status_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(15, 0))
        status_frame.columnconfigure(1, weight=1)
        
        ttk.Label(status_frame, text="状态:", font=('Arial', 10)).grid(row=0, column=0, padx=(5, 0))
        self.status_var = tk.StringVar(value="请选择PDF文件开始编辑")
        ttk.Label(status_frame, textvariable=self.status_var, font=('Arial', 10)).grid(row=0, column=1, sticky=tk.W, padx=(5, 0))
        
    def update_status(self, message, color="black"):
        """更新状态信息"""
        self.status_var.set(message)
        self.root.update()
        
    def select_file(self):
        """选择PDF文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择PDF文件",
                filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
            )
            
            if file_path:
                self.update_status("正在打开PDF文件...", "blue")
                
                # 打开PDF
                self.pdf_editor.open_pdf(file_path)
                self.file_path_var.set(f"已打开: {Path(file_path).name}")
                
                # 提取文本
                self.update_status("正在提取PDF文本，保持原始布局...", "blue")
                text_content = self.pdf_editor.extract_text_with_layout()
                self.current_text = text_content
                
                # 更新预览
                self.preview_text.configure(state=tk.NORMAL)
                self.preview_text.delete(1.0, tk.END)
                self.preview_text.insert(1.0, text_content)
                self.preview_text.configure(state=tk.DISABLED)
                
                # 启用操作按钮
                self.replace_button.configure(state=tk.NORMAL)
                self.save_button.configure(state=tk.NORMAL)
                
                self.update_status(f"PDF加载成功，共 {len(self.pdf_editor.pdf_document)} 页", "green")
                
        except Exception as e:
            self.update_status(f"错误: {str(e)}", "red")
            messagebox.showerror("错误", f"文件加载失败:\n{str(e)}")
    
    def replace_text_action(self):
        """执行文本替换"""
        try:
            find_text = self.find_text.get("1.0", tk.END).strip()
            replace_text = self.replace_text.get("1.0", tk.END).strip()
            
            if not find_text:
                messagebox.showwarning("警告", "请输入要查找的文本")
                return
            
            self.update_status("正在执行文本替换，保持原始格式...", "blue")
            
            # 执行替换
            count = self.pdf_editor.replace_text_in_pdf(find_text, replace_text)
            
            if count > 0:
                # 更新历史记录
                self.history_text.configure(state=tk.NORMAL)
                history_entry = f"替换 {count} 处: '{find_text}' → '{replace_text}'\n"
                self.history_text.insert(tk.END, history_entry)
                self.history_text.configure(state=tk.DISABLED)
                self.history_text.see(tk.END)
                
                # 重新提取文本显示
                updated_text = self.pdf_editor.extract_text_with_layout()
                self.preview_text.configure(state=tk.NORMAL)
                self.preview_text.delete(1.0, tk.END)
                self.preview_text.insert(1.0, updated_text)
                self.preview_text.configure(state=tk.DISABLED)
                
                self.update_status(f"替换完成，共替换 {count} 处", "green")
            else:
                self.update_status("未找到要替换的文本", "orange")
                messagebox.showinfo("提示", "未找到要替换的文本")
                
        except Exception as e:
            self.update_status(f"替换失败: {str(e)}", "red")
            messagebox.showerror("错误", f"文本替换失败:\n{str(e)}")
    
    def save_pdf(self):
        """保存PDF文件"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="保存PDF文件",
                defaultextension=".pdf",
                filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")],
                initialfile="edited_document.pdf"
            )
            
            if file_path:
                self.update_status("正在保存PDF文件...", "blue")
                
                self.pdf_editor.save_pdf(file_path)
                
                self.update_status(f"PDF已保存: {Path(file_path).name}", "green")
                messagebox.showinfo("成功", f"PDF文件已保存至:\n{file_path}")
                
        except Exception as e:
            self.update_status(f"保存失败: {str(e)}", "red")
            messagebox.showerror("错误", f"PDF保存失败:\n{str(e)}")


def main():
    root = tk.Tk()
    app = AdvancedPDFEditorGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
