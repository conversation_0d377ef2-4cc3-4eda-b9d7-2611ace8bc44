#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装高级PDF处理库
基于GitHub最佳实践，安装格式保持相关的库
"""

import subprocess
import sys
import importlib

def install_package(package_name, import_name=None):
    """安装Python包"""
    if import_name is None:
        import_name = package_name
    
    try:
        # 先检查是否已安装
        importlib.import_module(import_name)
        print(f"✅ {package_name} - 已安装")
        return True
    except ImportError:
        pass
    
    try:
        print(f"📦 正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} - 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} - 安装失败: {e}")
        return False

def main():
    print("🚀 高级PDF处理库安装器")
    print("=" * 50)
    print("基于GitHub最佳实践，安装以下库：")
    print("1. PyMuPDF (fitz) - 最强大的PDF处理库")
    print("2. pdfplumber - 优秀的布局分析")
    print("3. MinerU (magic-pdf) - 高质量PDF转换")
    print("4. 其他辅助库")
    print("=" * 50)
    
    # 核心库列表
    libraries = [
        ("PyMuPDF", "fitz"),
        ("pdfplumber", "pdfplumber"),
        ("Pillow", "PIL"),
        ("numpy", "numpy"),
        ("opencv-python", "cv2"),
    ]
    
    # 高级库（可选）
    advanced_libraries = [
        ("magic-pdf", "magic_pdf"),
        ("layoutparser", "layoutparser"),
        ("detectron2", "detectron2"),
    ]
    
    print("\n📋 安装核心库...")
    success_count = 0
    total_count = len(libraries)
    
    for package, import_name in libraries:
        if install_package(package, import_name):
            success_count += 1
    
    print(f"\n📊 核心库安装结果: {success_count}/{total_count}")
    
    if success_count >= 2:  # 至少PyMuPDF和pdfplumber
        print("✅ 基本功能可用")
        
        # 询问是否安装高级库
        print("\n🔬 是否安装高级库？（用于更好的布局识别）")
        print("注意：这些库较大，安装时间较长")
        
        choice = input("安装高级库? (y/N): ").strip().lower()
        
        if choice == 'y':
            print("\n📋 安装高级库...")
            for package, import_name in advanced_libraries:
                install_package(package, import_name)
    
    print("\n" + "=" * 50)
    print("🎯 安装完成！")
    print("\n可用工具:")
    print("1. python advanced_pdf_editor.py - 高级PDF编辑器")
    print("2. python pdf_format_viewer.py - 格式对比查看器")
    print("3. python 启动PDF工具.py - 工具选择器")
    
    # 验证安装
    print("\n🔍 验证安装...")
    
    try:
        import fitz
        print(f"✅ PyMuPDF {fitz.VersionBind} - 可用")
        print("   支持功能: 直接PDF文本替换，格式保持")
    except ImportError:
        print("❌ PyMuPDF - 不可用")
    
    try:
        import pdfplumber
        print("✅ pdfplumber - 可用")
        print("   支持功能: 高质量文本提取，布局分析")
    except ImportError:
        print("❌ pdfplumber - 不可用")
    
    try:
        import magic_pdf
        print("✅ MinerU (magic-pdf) - 可用")
        print("   支持功能: 高质量PDF转换，布局保持")
    except ImportError:
        print("⚠️ MinerU - 未安装（可选）")
    
    print("\n🎉 准备就绪！现在可以使用高级PDF编辑功能了")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 安装已取消")
    except Exception as e:
        print(f"\n❌ 安装过程出错: {e}")
    
    input("\n按回车键退出...")
