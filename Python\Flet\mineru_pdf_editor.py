#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于MinerU的高质量PDF编辑工具
使用GitHub上的MinerU项目实现高质量PDF布局保持
参考: https://github.com/opendatalab/MinerU
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import warnings
from pathlib import Path
import json
import tempfile
import os

# 抑制警告
warnings.filterwarnings("ignore")

try:
    import fitz  # PyMuPDF作为备用
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

try:
    # 尝试导入MinerU
    from magic_pdf.pipe.UNIPipe import UNIPipe
    from magic_pdf.pipe.OCRPipe import OCRPipe
    from magic_pdf.pipe.TXTPipe import TXTPipe
    MINERU_AVAILABLE = True
except ImportError:
    MINERU_AVAILABLE = False

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False


class MinerUPDFProcessor:
    def __init__(self):
        self.current_pdf_path = None
        self.extracted_content = None
        self.layout_info = None
        
    def process_pdf_with_mineru(self, pdf_path):
        """使用MinerU处理PDF，保持高质量布局"""
        try:
            if not MINERU_AVAILABLE:
                raise Exception("MinerU未安装，请运行: pip install magic-pdf")
            
            # 使用MinerU的UNIPipe进行处理
            pipe = UNIPipe(
                pdf_path=pdf_path,
                output_dir=tempfile.mkdtemp(),
                mode="auto"  # 自动选择最佳模式
            )
            
            # 执行处理
            result = pipe.pipe_classify()
            
            if result:
                # 提取文本和布局信息
                content = pipe.pipe_analyze()
                self.extracted_content = content
                self.layout_info = pipe.get_layout_info()
                
                return self._format_mineru_output(content)
            else:
                raise Exception("MinerU处理失败")
                
        except Exception as e:
            # 如果MinerU失败，回退到PyMuPDF
            return self._fallback_extraction(pdf_path)
    
    def _format_mineru_output(self, content):
        """格式化MinerU的输出"""
        try:
            formatted_text = ""
            
            if isinstance(content, dict):
                # 处理结构化内容
                for page_num, page_content in content.items():
                    formatted_text += f"\n{'='*60}\n第 {page_num} 页\n{'='*60}\n"
                    
                    if isinstance(page_content, dict):
                        # 按布局顺序排列内容
                        if 'text_blocks' in page_content:
                            for block in page_content['text_blocks']:
                                formatted_text += block.get('text', '') + "\n"
                        
                        if 'tables' in page_content:
                            for table in page_content['tables']:
                                formatted_text += "\n[表格]\n"
                                formatted_text += table.get('text', '') + "\n"
                        
                        if 'images' in page_content:
                            for img in page_content['images']:
                                formatted_text += f"\n[图片: {img.get('caption', '无标题')}]\n"
                    else:
                        formatted_text += str(page_content) + "\n"
            else:
                formatted_text = str(content)
            
            return formatted_text
            
        except Exception as e:
            return f"格式化输出失败: {str(e)}"
    
    def _fallback_extraction(self, pdf_path):
        """备用提取方法"""
        try:
            if PYMUPDF_AVAILABLE:
                return self._extract_with_pymupdf(pdf_path)
            elif PDFPLUMBER_AVAILABLE:
                return self._extract_with_pdfplumber(pdf_path)
            else:
                raise Exception("没有可用的PDF处理库")
        except Exception as e:
            raise Exception(f"备用提取失败: {str(e)}")
    
    def _extract_with_pymupdf(self, pdf_path):
        """使用PyMuPDF提取"""
        doc = fitz.open(pdf_path)
        text = ""
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            text += f"\n{'='*60}\n第 {page_num + 1} 页\n{'='*60}\n"
            
            # 使用字典模式获取更好的布局
            blocks = page.get_text("dict")
            for block in blocks["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        line_text = ""
                        for span in line["spans"]:
                            line_text += span["text"]
                        if line_text.strip():
                            text += line_text + "\n"
                    text += "\n"
        
        doc.close()
        return text
    
    def _extract_with_pdfplumber(self, pdf_path):
        """使用pdfplumber提取"""
        text = ""
        
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                text += f"\n{'='*60}\n第 {page_num + 1} 页\n{'='*60}\n"
                
                # 使用layout参数保持格式
                page_text = page.extract_text(layout=True, x_tolerance=3, y_tolerance=3)
                if page_text:
                    text += page_text + "\n"
        
        return text


class MinerUPDFEditorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("MinerU高质量PDF编辑器 - GitHub最佳实践")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)
        
        # 初始化处理器
        self.processor = MinerUPDFProcessor()
        self.current_text = ""
        
        # 检查依赖
        self.check_dependencies()
        
        self.create_widgets()
    
    def check_dependencies(self):
        """检查依赖库"""
        missing_libs = []
        
        if not MINERU_AVAILABLE:
            missing_libs.append("MinerU (magic-pdf)")
        if not PYMUPDF_AVAILABLE:
            missing_libs.append("PyMuPDF")
        if not PDFPLUMBER_AVAILABLE:
            missing_libs.append("pdfplumber")
        
        if missing_libs:
            message = "以下库未安装，可能影响功能:\n" + "\n".join(f"• {lib}" for lib in missing_libs)
            message += "\n\n建议运行: python install_advanced_libs.py"
            messagebox.showwarning("依赖检查", message)
    
    def create_widgets(self):
        """创建界面控件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题区域
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        title_label = ttk.Label(
            title_frame, 
            text="🔬 MinerU高质量PDF编辑器", 
            font=('Arial', 18, 'bold')
        )
        title_label.pack()
        
        subtitle_label = ttk.Label(
            title_frame, 
            text="基于GitHub开源项目MinerU，实现高质量PDF布局保持和文本处理",
            font=('Arial', 10),
            foreground="gray"
        )
        subtitle_label.pack(pady=(5, 0))
        
        # 状态指示
        status_indicator_frame = ttk.Frame(title_frame)
        status_indicator_frame.pack(pady=(10, 0))
        
        # 显示可用功能
        if MINERU_AVAILABLE:
            ttk.Label(status_indicator_frame, text="✅ MinerU", foreground="green").pack(side=tk.LEFT, padx=(0, 10))
        else:
            ttk.Label(status_indicator_frame, text="❌ MinerU", foreground="red").pack(side=tk.LEFT, padx=(0, 10))
        
        if PYMUPDF_AVAILABLE:
            ttk.Label(status_indicator_frame, text="✅ PyMuPDF", foreground="green").pack(side=tk.LEFT, padx=(0, 10))
        else:
            ttk.Label(status_indicator_frame, text="❌ PyMuPDF", foreground="red").pack(side=tk.LEFT, padx=(0, 10))
        
        if PDFPLUMBER_AVAILABLE:
            ttk.Label(status_indicator_frame, text="✅ pdfplumber", foreground="green").pack(side=tk.LEFT)
        else:
            ttk.Label(status_indicator_frame, text="❌ pdfplumber", foreground="red").pack(side=tk.LEFT)
        
        # 文件操作区域
        file_frame = ttk.LabelFrame(main_frame, text="📁 文件操作", padding="10")
        file_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Button(file_frame, text="选择PDF文件", command=self.select_file).grid(row=0, column=0, padx=(0, 10))
        
        self.file_path_var = tk.StringVar(value="未选择文件")
        ttk.Label(file_frame, textvariable=self.file_path_var, foreground="gray").grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        ttk.Button(file_frame, text="安装依赖", command=self.install_dependencies).grid(row=0, column=2, padx=(10, 0))
        
        # 主要内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        content_frame.columnconfigure(0, weight=2)
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # 左侧：文本显示
        text_frame = ttk.LabelFrame(content_frame, text="📄 高质量文本提取结果", padding="10")
        text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.text_display = scrolledtext.ScrolledText(
            text_frame,
            wrap=tk.WORD,
            width=70,
            height=35,
            font=('Consolas', 10),
            state=tk.DISABLED
        )
        self.text_display.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 右侧：操作面板
        operation_frame = ttk.LabelFrame(content_frame, text="🛠️ 操作面板", padding="10")
        operation_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        operation_frame.columnconfigure(0, weight=1)
        
        # 处理选项
        ttk.Label(operation_frame, text="处理模式:", font=('Arial', 11, 'bold')).grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        self.mode_var = tk.StringVar(value="auto")
        mode_frame = ttk.Frame(operation_frame)
        mode_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        ttk.Radiobutton(mode_frame, text="自动模式", variable=self.mode_var, value="auto").pack(anchor=tk.W)
        ttk.Radiobutton(mode_frame, text="OCR模式", variable=self.mode_var, value="ocr").pack(anchor=tk.W)
        ttk.Radiobutton(mode_frame, text="文本模式", variable=self.mode_var, value="txt").pack(anchor=tk.W)
        
        # 处理按钮
        self.process_button = ttk.Button(
            operation_frame, 
            text="🚀 开始处理", 
            command=self.process_pdf,
            state=tk.DISABLED
        )
        self.process_button.grid(row=2, column=0, pady=(0, 20))
        
        # 导出选项
        export_frame = ttk.LabelFrame(operation_frame, text="📤 导出选项", padding="5")
        export_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        export_frame.columnconfigure(0, weight=1)
        
        ttk.Button(export_frame, text="导出为文本", command=self.export_text).grid(row=0, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(export_frame, text="导出为Markdown", command=self.export_markdown).grid(row=1, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(export_frame, text="导出为JSON", command=self.export_json).grid(row=2, column=0, sticky=(tk.W, tk.E), pady=2)
        
        # 信息显示
        info_frame = ttk.LabelFrame(operation_frame, text="ℹ️ 处理信息", padding="5")
        info_frame.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        info_frame.columnconfigure(0, weight=1)
        info_frame.rowconfigure(0, weight=1)
        
        self.info_text = scrolledtext.ScrolledText(
            info_frame,
            width=30,
            height=15,
            font=('Consolas', 9),
            state=tk.DISABLED
        )
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        status_frame = ttk.Frame(main_frame, relief=tk.SUNKEN, borderwidth=1)
        status_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(15, 0))
        status_frame.columnconfigure(1, weight=1)
        
        ttk.Label(status_frame, text="状态:", font=('Arial', 10)).grid(row=0, column=0, padx=(5, 0))
        self.status_var = tk.StringVar(value="请选择PDF文件开始处理")
        ttk.Label(status_frame, textvariable=self.status_var, font=('Arial', 10)).grid(row=0, column=1, sticky=tk.W, padx=(5, 0))
    
    def update_status(self, message):
        """更新状态信息"""
        self.status_var.set(message)
        self.root.update()
    
    def add_info(self, message):
        """添加信息到信息面板"""
        self.info_text.configure(state=tk.NORMAL)
        self.info_text.insert(tk.END, f"{message}\n")
        self.info_text.configure(state=tk.DISABLED)
        self.info_text.see(tk.END)
    
    def select_file(self):
        """选择PDF文件"""
        file_path = filedialog.askopenfilename(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.processor.current_pdf_path = file_path
            self.file_path_var.set(f"已选择: {Path(file_path).name}")
            self.process_button.configure(state=tk.NORMAL)
            self.add_info(f"选择文件: {Path(file_path).name}")
            self.update_status("文件已选择，点击开始处理")
    
    def process_pdf(self):
        """处理PDF文件"""
        if not self.processor.current_pdf_path:
            messagebox.showwarning("警告", "请先选择PDF文件")
            return
        
        try:
            self.update_status("正在使用MinerU处理PDF...")
            self.add_info("开始处理PDF文件...")
            
            # 处理PDF
            result_text = self.processor.process_pdf_with_mineru(self.processor.current_pdf_path)
            self.current_text = result_text
            
            # 显示结果
            self.text_display.configure(state=tk.NORMAL)
            self.text_display.delete(1.0, tk.END)
            self.text_display.insert(1.0, result_text)
            self.text_display.configure(state=tk.DISABLED)
            
            self.add_info("PDF处理完成")
            self.update_status("处理完成，可以导出结果")
            
        except Exception as e:
            error_msg = f"处理失败: {str(e)}"
            self.add_info(error_msg)
            self.update_status(error_msg)
            messagebox.showerror("错误", f"PDF处理失败:\n{str(e)}")
    
    def export_text(self):
        """导出为文本文件"""
        if not self.current_text:
            messagebox.showwarning("警告", "没有可导出的内容")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="导出文本文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.current_text)
                self.add_info(f"导出文本: {Path(file_path).name}")
                messagebox.showinfo("成功", f"文本已导出至:\n{file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败:\n{str(e)}")
    
    def export_markdown(self):
        """导出为Markdown文件"""
        if not self.current_text:
            messagebox.showwarning("警告", "没有可导出的内容")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="导出Markdown文件",
            defaultextension=".md",
            filetypes=[("Markdown文件", "*.md"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                # 简单的Markdown格式化
                markdown_text = self.current_text.replace("=====", "## ")
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(markdown_text)
                self.add_info(f"导出Markdown: {Path(file_path).name}")
                messagebox.showinfo("成功", f"Markdown已导出至:\n{file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败:\n{str(e)}")
    
    def export_json(self):
        """导出为JSON文件"""
        if not self.processor.extracted_content:
            messagebox.showwarning("警告", "没有可导出的结构化内容")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="导出JSON文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.processor.extracted_content, f, ensure_ascii=False, indent=2)
                self.add_info(f"导出JSON: {Path(file_path).name}")
                messagebox.showinfo("成功", f"JSON已导出至:\n{file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败:\n{str(e)}")
    
    def install_dependencies(self):
        """安装依赖库"""
        try:
            import subprocess
            import sys
            
            self.update_status("正在安装依赖库...")
            self.add_info("启动依赖安装程序...")
            
            # 运行安装脚本
            result = subprocess.run([sys.executable, "install_advanced_libs.py"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                self.add_info("依赖安装完成")
                messagebox.showinfo("成功", "依赖库安装完成，请重启程序")
            else:
                self.add_info(f"安装失败: {result.stderr}")
                messagebox.showerror("错误", f"依赖安装失败:\n{result.stderr}")
                
        except Exception as e:
            error_msg = f"安装过程出错: {str(e)}"
            self.add_info(error_msg)
            messagebox.showerror("错误", error_msg)


def main():
    root = tk.Tk()
    app = MinerUPDFEditorGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
