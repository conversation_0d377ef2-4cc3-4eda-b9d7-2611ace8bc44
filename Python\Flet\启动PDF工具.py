#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF工具启动器 - 选择不同的PDF处理工具
"""

import sys
import subprocess
import warnings

# 抑制字体相关警告
warnings.filterwarnings("ignore", message=".*FontBBox.*")
warnings.filterwarnings("ignore", message=".*font descriptor.*")

def show_menu():
    """显示工具选择菜单"""
    print("=" * 60)
    print("🔧 PDF文本处理工具集")
    print("=" * 60)
    print()
    print("📋 基础工具:")
    print("1. 📝 PDF文本替换编辑工具 (tkinter版本)")
    print("   - 功能：PDF文本提取、查找替换、保存新PDF")
    print("   - 特点：稳定可靠，内置格式优化")
    print()
    print("2. ✨ PDF文本替换编辑工具 (CustomTkinter版本)")
    print("   - 功能：PDF文本提取、查找替换、保存新PDF")
    print("   - 特点：现代化界面，美观易用")
    print()
    print("3. 🔍 PDF格式查看器")
    print("   - 功能：对比原始提取格式与优化后格式")
    print("   - 特点：帮助了解PDF文本提取后的格式变化")
    print()
    print("🚀 高级工具 (基于GitHub最佳实践):")
    print("4. 🔬 高级PDF编辑器 (PyMuPDF)")
    print("   - 功能：直接在PDF中替换文本，完美保持格式")
    print("   - 特点：基于PyMuPDF，GitHub最受欢迎的PDF库")
    print()
    print("5. 🎯 MinerU高质量PDF处理器")
    print("   - 功能：使用MinerU实现高质量布局保持")
    print("   - 特点：GitHub开源项目，专业级PDF处理")
    print()
    print("🛠️ 系统工具:")
    print("6. 📦 安装高级依赖库")
    print("   - 功能：一键安装PyMuPDF、MinerU等高级库")
    print("   - 特点：解决格式混乱问题的根本方案")
    print()
    print("7. 🚀 智能启动器")
    print("   - 功能：自动检测依赖，选择最佳GUI版本")
    print("   - 特点：一键安装缺失库，智能推荐")
    print()
    print("8. 🧪 测试工具")
    print("   - 功能：验证所有工具是否正常工作")
    print("   - 特点：检查依赖库、语法、文件对话框")
    print()
    print("0. 退出")
    print("=" * 60)

def run_tool(choice):
    """运行选定的工具"""
    tools = {
        "1": {
            "name": "PDF文本替换编辑工具 (tkinter版本)",
            "script": "pdf_editor_tkinter.py",
            "description": "启动tkinter版本的PDF编辑工具"
        },
        "2": {
            "name": "PDF文本替换编辑工具 (CustomTkinter版本)",
            "script": "pdf_editor_customtkinter.py",
            "description": "启动CustomTkinter版本的PDF编辑工具"
        },
        "3": {
            "name": "PDF格式查看器",
            "script": "pdf_format_viewer.py",
            "description": "启动PDF格式对比查看器"
        },
        "4": {
            "name": "高级PDF编辑器 (PyMuPDF)",
            "script": "advanced_pdf_editor.py",
            "description": "启动基于PyMuPDF的高级PDF编辑器"
        },
        "5": {
            "name": "MinerU高质量PDF处理器",
            "script": "mineru_pdf_editor.py",
            "description": "启动基于MinerU的高质量PDF处理器"
        },
        "6": {
            "name": "安装高级依赖库",
            "script": "install_advanced_libs.py",
            "description": "安装PyMuPDF、MinerU等高级库"
        },
        "7": {
            "name": "智能启动器",
            "script": "install_and_run_gui.py",
            "description": "启动智能GUI选择器"
        },
        "8": {
            "name": "测试工具",
            "script": "test_gui_fix.py",
            "description": "运行工具测试"
        }
    }
    
    if choice in tools:
        tool = tools[choice]
        print(f"\n🚀 正在启动: {tool['name']}")
        print(f"📝 描述: {tool['description']}")
        print("-" * 50)
        
        try:
            # 运行选定的工具
            result = subprocess.run([sys.executable, tool['script']], 
                                  capture_output=False, 
                                  text=True)
            
            if result.returncode == 0:
                print(f"\n✅ {tool['name']} 已正常退出")
            else:
                print(f"\n⚠️ {tool['name']} 退出时出现问题 (退出码: {result.returncode})")
                
        except FileNotFoundError:
            print(f"\n❌ 找不到文件: {tool['script']}")
            print("请确保所有工具文件都在当前目录中")
            
        except Exception as e:
            print(f"\n❌ 启动失败: {e}")
            
    else:
        print("\n❌ 无效选择，请重新输入")

def check_dependencies():
    """检查基础依赖"""
    print("🔍 检查基础依赖库...")
    
    required_libs = ["tkinter", "PyPDF2", "pdfplumber", "reportlab"]
    missing_libs = []
    
    for lib in required_libs:
        try:
            if lib == "tkinter":
                import tkinter
            elif lib == "PyPDF2":
                import PyPDF2
            elif lib == "pdfplumber":
                import pdfplumber
            elif lib == "reportlab":
                import reportlab
            print(f"✅ {lib}")
        except ImportError:
            missing_libs.append(lib)
            print(f"❌ {lib}")
    
    if missing_libs:
        print(f"\n⚠️ 缺少依赖库: {', '.join(missing_libs)}")
        print("建议运行: pip install PyPDF2 pdfplumber reportlab")
        if "tkinter" in missing_libs:
            print("注意: tkinter通常随Python安装，如果缺失请重新安装Python")
        print()
        return False
    else:
        print("✅ 所有基础依赖库都已安装\n")
        return True

def main():
    print("PDF文本处理工具集 v2.0")
    print("解决FontBBox字体问题，支持格式优化和对比")
    print()
    
    # 检查依赖
    deps_ok = check_dependencies()
    
    while True:
        show_menu()
        
        try:
            choice = input("请选择工具 (0-8): ").strip()
            
            if choice == "0":
                print("\n👋 再见！")
                break
            elif choice in ["1", "2", "3", "4", "5", "6", "7", "8"]:
                if not deps_ok and choice in ["1", "2", "3", "4", "5"]:
                    print("\n⚠️ 检测到缺少依赖库，建议先选择选项6安装高级依赖")
                    continue_anyway = input("是否继续运行? (y/N): ").strip().lower()
                    if continue_anyway != 'y':
                        continue
                
                run_tool(choice)
                
                # 询问是否继续
                print("\n" + "=" * 60)
                continue_choice = input("是否继续使用其他工具? (Y/n): ").strip().lower()
                if continue_choice == 'n':
                    print("\n👋 再见！")
                    break
                print()
            else:
                print("\n❌ 无效选择，请输入 0-8 之间的数字")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已取消，再见！")
            break
        except Exception as e:
            print(f"\n❌ 程序出错: {e}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"启动器出错: {e}")
        input("按回车键退出...")
