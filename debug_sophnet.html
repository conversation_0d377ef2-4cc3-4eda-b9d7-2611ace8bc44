<!DOCTYPE html>
<html class="layout-theme-light" lang="en">
 <head>
  <script>
   self["MonacoEnvironment"] = (function (paths) {
          return {
            globalAPI: false,
            getWorkerUrl : function (moduleId, label) {
              var result =  paths[label];
              if (/^((http:)|(https:)|(file:)|(\/\/))/.test(result)) {
                var currentUrl = String(window.location);
                var currentOrigin = currentUrl.substr(0, currentUrl.length - window.location.hash.length - window.location.search.length - window.location.pathname.length);
                if (result.substring(0, currentOrigin.length) !== currentOrigin) {
                  var js = '/*' + label + '*/importScripts("' + result + '");';
                  var blob = new Blob([js], { type: 'application/javascript' });
                  return URL.createObjectURL(blob);
                }
              }
              return result;
            }
          };
        })({
  "editorWorkerService": "/monacoeditorwork/editor.worker.bundle.js",
  "css": "/monacoeditorwork/css.worker.bundle.js",
  "html": "/monacoeditorwork/html.worker.bundle.js",
  "json": "/monacoeditorwork/json.worker.bundle.js",
  "typescript": "/monacoeditorwork/ts.worker.bundle.js",
  "javascript": "/monacoeditorwork/ts.worker.bundle.js",
  "less": "/monacoeditorwork/css.worker.bundle.js",
  "scss": "/monacoeditorwork/css.worker.bundle.js",
  "handlebars": "/monacoeditorwork/html.worker.bundle.js",
  "razor": "/monacoeditorwork/html.worker.bundle.js"
});
  </script>
  <meta charset="utf-8"/>
  <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible"/>
  <meta content="webkit" name="renderer"/>
  <meta content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport"/>
  <meta content="SophNet - 几行代码，接入全球最顶尖的AI大模型。算能自研算力加持， 更快！更稳！更省！注册即送千万Tokens！SophNet（网址：sophnet.com）致力于打造更快、更稳、更省的一站式模型推理API服务平台，用户可以创建符合他们应用需求的各种工作流拓扑，从简单的API服务到复杂的 Agent 智能体，都能实现。现已上线DeepSeek R1 满血版、阿里通以千问QwQ-32B、DeepSeek V3 0324模型！" name="description"/>
  <meta content="codeva-xFe0dLaulA" name="baidu-site-verification"/>
  <meta content="SophNet, sophnet.com, DeepSeek R1 满血版, DeepSeek V3 0324, Qwen, 大模型, AI大模型, 开源大模型, 大模型调用, 大模型能力开放平台, 开放平台, deepseek api稳定版, deepseek 稳定版, 国产算力底座, 算能, S0PHGO, deepseek最快版, deepseek服务器繁忙, 云算力, AI计算, AI推理, AI训练, 算能, SOPHGO, AI模型服务, AI云平台, TPU, AI推理平台, AI训练平台, 云计算, 人工智能, 机器学习, 深度学习, AI推理优化, AI训练优化, AI推理加速, AI训练加速, 高性能计算, AI成本降低, AI模型托管, AI自动化运维, AI弹性扩展, AI基础设施, AI GPU 替代方案, AI计算优化, AI计算普惠, AI行业解决方案, AI云部署, AI云计算, AI大模型推理, AI大模型训练, AI开发者平台, AI API, AI智能体, AI API 服务, AI企业解决方案, AI SaaS, AI推理引擎, AI推理框架, AI推理云, AI推理任务, AI推理高效, AI推理稳定, AI推理低成本, AI推理云计算, AI推理平台推荐, AI推理性能优化, AI推理应用, AI推理工具, AI推理算法, AI推理芯片, AI推理负载均衡, AI推理加速器, AI推理算力, AI推理能力, AI推理成本控制, AI推理速度, AI推理响应, AI推理 API, AI推理容器化, AI推理分布式, AI推理低延迟, AI推理高吞吐, AI推理模型, AI推理智能调度, AI推理优化服务, AI推理负载管理, AI推理扩展, AI推理计算架构, AI推理硬件优化, AI推理云平台, AI推理性能, AI推理生态, AI推理一站式, AI推理在线, AI推理技术, AI推理高效计算, AI推理低功耗, AI推理多任务处理, AI推理高吞吐量, AI推理API支持, AI推理SDK, AI推理服务, AI推理服务器, AI推理效率提升, AI推理体验优化, AI推理企业级, AI推理成本优化, AI推理可扩展, AI推理安全性, AI推理隐私保护, AI推理计算加速, AI推理开放平台, AI推理高并发, AI推理容器化部署, AI推理微服务, AI推理智能管理, AI推理快速部署, AI推理业务场景, AI推理软件优化, AI推理创新, AI推理自动扩展, AI推理高适配性, AI推理智能优化, AI推理兼容性, AI推理低功耗计算, AI推理高效架构, AI推理模型加载, AI推理多层架构, AI推理智能分发, AI推理高效存储, AI推理智能调度, AI推理可视化管理, AI推理计算优化, AI推理云托管, AI推理低运维成本, AI推理企业级解决方案, AI推理智能运维, AI推理跨平台兼容, AI推理高兼容性, AI推理业务集成, AI推理端到端解决方案, AI推理智能分配, AI推理工作流, AI推理智能监控, AI推理存储优化, AI推理实时处理, AI推理快速响应, AI推理计算智能化, AI推理算力池, AI推理模型支持, AI推理灵活调度, AI推理开发者工具, AI推理企业应用, AI推理高效分配, AI推理智能资源管理, AI推理全栈优化, AI推理算力整合, AI推理在线训练, AI推理API云服务, AI推理行业定制, AI推理智能任务调度, AI推理资源优化, AI推理智能管理平台, AI推理低延迟方案, AI推理云端优化, AI推理智能任务, AI推理智能运维管理, AI推理算力弹性, AI推理开放生态, AI推理计算资源, AI推理数据安全, AI推理自动任务调度, AI推理企业智能化, AI推理云计算架构, AI推理智能计算平台, AI推理开放标准, AI推理数据保护, AI推理用户体验优化, AI推理智能化平台, AI推理任务管理, AI推理一体化, AI推理高并发处理, AI推理自研芯片, AI推理企业级安全, AI推理应用创新, AI推理开发者体验, AI推理生态系统, AI推理端到端, AI推理多模型支持, AI推理企业智能, AI推理计算云, AI推理低延迟部署, AI推理算力服务, AI推理算力集群, AI推理智能调优, AI推理开发生态, AI推理计算性能优化, AI推理智能适配, AI推理企业场景, AI推理实时计算, AI推理软硬件协同, AI推理企业解决方案, AI推理存储智能化, AI推理全自动化, AI推理低功耗算力, AI推理全栈架构, AI推理数据中心优化, AI推理智能管理系统, AI推理智能配置, AI推理多任务调度, AI推理智能负载均衡, AI推理计算能力, AI推理弹性扩展, AI推理智能计算资源, AI推理智能化方案, AI推理计算服务, AI推理智能算力管理, AI推理高效管理, AI推理数据处理, AI推理云端安全, AI推理智能算力优化, AI推理自动化管理, AI推理行业落地, AI推理应用落地, AI推理生态体系" name="keywords"/>
  <title>
   SophNet | 云算力平台
  </title>
  <link href="/logo.svg" rel="icon"/>
  <script>
   document.addEventListener("DOMContentLoaded", function () {
        // 检测是否为移动设备
        const isMobile =
          /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
            navigator.userAgent
          );
        const hidePage = location.hash.startsWith("#/agentChat");
        // 只在非移动设备上加载聊天按钮
        if (!isMobile && !hidePage) {
          var scriptEl = document.createElement("script");
          scriptEl.src =
            "https://sophnet.com/api/agents/sophnet-chat-button.umd.js";
          scriptEl.defer = true;
          scriptEl.onload = function () {
            if (window.FloatingChatButton) {
              window.FloatingChatButton.mount(undefined, {
                id: "1PMyyRwwv7WidnSG7ZYujg", // 当前智能体ID，请勿修改
                name: "SophNet AI客服" // 窗体左上角标题，可自定义修改
              });
            }
          };
          document.body.appendChild(scriptEl);
        }
      });
  </script>
  <script src="https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js" type="text/javascript">
  </script>
  <script async="" crossorigin="anonymous" integrity="sha384-Rma6DA2IPUwhNxmrB/7S3Tno0YY7sFu9WSYMCuulLhIqYSGZ2gKCJWIqhBWqMQfh" src="https://cdn.jsdelivr.net/npm/katex@0.16.21/dist/katex.min.js">
  </script>
  <script async="" src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js">
  </script>
  <script defer="">
   var _hmt = _hmt || [];
      (function () {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?70c35e2a4eaa61e3234d43ac7a305148";
        hm.async = true;
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
      })();
  </script>
  <script>
   window.process = {};
  </script>
  <script crossorigin="" src="/static/js/index-CXULuwdP.js" type="module">
  </script>
  <link crossorigin="" href="/static/css/index-8Lxj40Tp.css" rel="stylesheet"/>
  <link href="/assets/layout-theme-light.css" id="theme-link-tag" rel="stylesheet"/>
 </head>
 <body>
  <div id="app">
   <style>
    #app,body,html{align-items:center;display:flex;height:100%;justify-content:center;overflow:hidden;position:relative;width:100%}.loader,.loader:after,.loader:before{animation:load-animation 1.8s ease-in-out infinite;animation-fill-mode:both;border-radius:50%;height:2.5em;width:2.5em}.loader{animation-delay:-.16s;color:#6200ee;font-size:10px;margin:80px auto;position:relative;text-indent:-9999em;top:0;transform:translateZ(0);transform:translate(-50%)}.loader:after,.loader:before{content:"";position:absolute;top:0}.loader:before{animation-delay:-.32s;left:-3.5em}.loader:after{left:3.5em}@keyframes load-animation{0%,80%,to{box-shadow:0 2.5em 0 -1.3em}40%{box-shadow:0 2.5em 0 0}}
   </style>
   <div class="loader">
   </div>
  </div>
 </body>
</html>
