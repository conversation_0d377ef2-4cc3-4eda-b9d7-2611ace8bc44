#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import itertools
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import msoffcrypto
import io
import sys
import os

# 修复Windows控制台编码问题
if sys.platform.startswith('win'):
    # 设置控制台编码为UTF-8
    os.system('chcp 65001 > nul')
    # 重新配置stdout编码
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')

class DocPasswordCracker:
    def __init__(self):
        # 数字对应的拼音映射
        self.digit_pinyin = {
            '0': 'ling',
            '1': 'yi', 
            '2': 'er',
            '3': 'san',
            '4': 'si',
            '5': 'wu',
            '6': 'liu',
            '7': 'qi',
            '8': 'ba',
            '9': 'jiu'
        }
        
        self.found_password = None
        self.attempts = 0
        self.start_time = None
    
    def safe_print(self, text):
        """安全打印，避免编码错误"""
        try:
            print(text)
        except UnicodeEncodeError:
            # 移除emoji和特殊字符，只保留基本字符
            safe_text = text.encode('ascii', 'ignore').decode('ascii')
            print(safe_text)
        
    def generate_passwords(self, min_length=3, max_length=5):
        """生成所有可能的密码组合"""
        passwords = []
        
        # 生成3-5位数字的所有组合
        for length in range(min_length, max_length + 1):
            for digits in itertools.product('0123456789', repeat=length):
                # 转换为拼音
                pinyin_password = ''.join(self.digit_pinyin[d] for d in digits)
                passwords.append(pinyin_password)
                
                # 也尝试数字本身
                digit_password = ''.join(digits)
                passwords.append(digit_password)
        
        return passwords
    
    def try_password(self, file_path, password):
        """尝试单个密码"""
        try:
            with open(file_path, 'rb') as f:
                office_file = msoffcrypto.OfficeFile(f)
                office_file.load_key(password=password)
                
                # 尝试解密到内存
                decrypted = io.BytesIO()
                office_file.decrypt(decrypted)
                
                return password  # 成功解密
        except:
            return None
    
    def crack_password_threaded(self, file_path, max_workers=8):
        """多线程破解密码"""
        self.safe_print("开始生成密码字典...")
        passwords = self.generate_passwords()
        self.safe_print(f"生成了 {len(passwords)} 个可能的密码")
        
        self.safe_print("开始多线程破解...")
        self.start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_password = {
                executor.submit(self.try_password, file_path, pwd): pwd 
                for pwd in passwords
            }
            
            # 处理结果
            for future in as_completed(future_to_password):
                self.attempts += 1
                password = future_to_password[future]
                
                # 显示进度
                if self.attempts % 100 == 0:
                    elapsed = time.time() - self.start_time
                    speed = self.attempts / elapsed
                    self.safe_print(f"已尝试 {self.attempts}/{len(passwords)} 个密码 "
                          f"(速度: {speed:.1f} 密码/秒)")
                
                try:
                    result = future.result()
                    if result:
                        self.found_password = result
                        self.safe_print(f"\n密码破解成功！")
                        self.safe_print(f"密码是: {result}")
                        
                        # 取消其他任务
                        for f in future_to_password:
                            f.cancel()
                        
                        return result
                except Exception as e:
                    continue
        
        self.safe_print(f"\n密码破解失败，已尝试所有 {len(passwords)} 个可能的密码")
        return None
    
    def crack_password_optimized(self, file_path):
        """优化的破解方法 - 按概率排序"""
        self.safe_print("开始智能密码破解...")
        
        # 按使用频率排序的常见数字组合
        common_patterns = [
            # 常见的3位数字
            ['123', '111', '000', '666', '888', '999'],
            # 常见的4位数字  
            ['1234', '0000', '1111', '2222', '6666', '8888', '1314', '5201'],
            # 常见的5位数字
            ['12345', '00000', '11111', '88888', '66666']
        ]
        
        passwords_to_try = []
        
        # 优先尝试常见组合
        for pattern_group in common_patterns:
            for pattern in pattern_group:
                # 拼音版本
                pinyin = ''.join(self.digit_pinyin[d] for d in pattern)
                passwords_to_try.append(pinyin)
                # 数字版本
                passwords_to_try.append(pattern)
        
        # 然后尝试所有其他组合
        all_passwords = self.generate_passwords()
        for pwd in all_passwords:
            if pwd not in passwords_to_try:
                passwords_to_try.append(pwd)
        
        self.safe_print(f"将按优先级尝试 {len(passwords_to_try)} 个密码")
        
        self.start_time = time.time()
        
        for i, password in enumerate(passwords_to_try):
            self.attempts += 1
            
            if self.attempts % 50 == 0:
                elapsed = time.time() - self.start_time
                speed = self.attempts / elapsed
                self.safe_print(f"已尝试 {self.attempts}/{len(passwords_to_try)} 个密码 "
                      f"(速度: {speed:.1f} 密码/秒) - 当前: {password}")
            
            if self.try_password(file_path, password):
                self.safe_print(f"\n密码破解成功！")
                self.safe_print(f"密码是: {password}")
                return password
        
        self.safe_print(f"\n密码破解失败")
        return None

def main():
    print("=" * 60)
    print("Word文档密码破解工具 (数字拼音专版)")
    print("=" * 60)
    
    # 检查依赖
    try:
        import msoffcrypto
    except ImportError:
        print("缺少依赖库，请安装:")
        print("pip install msoffcrypto-tool")
        return
    
    file_path = input("请输入Word文档路径: ").strip().strip('"')
    
    if not file_path:
        print("文件路径不能为空")
        return
    
    try:
        cracker = DocPasswordCracker()
        
        print("\n选择破解模式:")
        print("1. 智能破解 (按概率优先，推荐)")
        print("2. 多线程暴力破解 (更快但占用资源)")
        
        choice = input("请选择 (1/2): ").strip()
        
        if choice == '2':
            # 多线程破解
            workers = input("线程数 (默认8): ").strip()
            workers = int(workers) if workers.isdigit() else 8
            result = cracker.crack_password_threaded(file_path, workers)
        else:
            # 智能破解
            result = cracker.crack_password_optimized(file_path)
        
        if result:
            elapsed = time.time() - cracker.start_time
            print(f"\n破解统计:")
            print(f"   密码: {result}")
            print(f"   尝试次数: {cracker.attempts}")
            print(f"   用时: {elapsed:.2f} 秒")
            print(f"   平均速度: {cracker.attempts/elapsed:.1f} 密码/秒")
        
    except FileNotFoundError:
        print("文件不存在，请检查路径")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    main()