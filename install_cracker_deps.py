import subprocess
import sys

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    print("🔧 安装Word文档密码破解工具依赖...")
    
    packages = [
        "msoffcrypto-tool",
        "python-docx",
        "olefile"
    ]
    
    for package in packages:
        print(f"📦 安装 {package}...")
        if install_package(package):
            print(f"✅ {package} 安装成功")
        else:
            print(f"❌ {package} 安装失败")
    
    print("\n🎉 依赖安装完成！现在可以运行破解工具了")
    print("运行命令: python doc_password_cracker.py")

if __name__ == "__main__":
    main()